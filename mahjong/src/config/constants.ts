import { TileType } from '../types/tile';

export const GAME_CONFIG = {
  TILE_SIZE: 60,
  TILE_SIZE_MOBILE: 45,
  TILE_DEPTH: 6,
  BOARD_PADDING: 32,
  BOARD_GAP: 8,
  ANIMATION_DURATION: 250,
  MAX_HINTS: 3,
  MAX_SHUFFLES: 2,
  SCORE_PER_MATCH: 10,
  SCORE_HINT_PENALTY: 5,
  SCORE_SHUFFLE_PENALTY: 10,
} as const;

export const TILE_SYMBOLS = {
  [TileType.Dots]: ['⚀', '⚁', '⚂', '⚃', '⚄', '⚅', '⚆', '⚇', '⚈'],
  [TileType.Bamboo]: ['🎋', '🎍', '🌿', '🌱', '🌾', '🎋', '🎍', '🌿', '🌱'],
  [TileType.Characters]: ['一', '二', '三', '四', '五', '六', '七', '八', '九'],
  [TileType.Winds]: ['東', '南', '西', '北'],
  [TileType.Dragons]: ['中', '發', '白'],
  [TileType.Flowers]: ['🌸', '🌺', '🌻', '🌷'],
  [TileType.Seasons]: ['🌸', '☀️', '🍂', '❄️'],
} as const;

export const TILE_COLORS = {
  [TileType.Dots]: 'var(--tile-dots)',
  [TileType.Bamboo]: 'var(--tile-bamboo)',
  [TileType.Characters]: 'var(--tile-characters)',
  [TileType.Winds]: 'var(--tile-winds)',
  [TileType.Dragons]: 'var(--tile-dragons)',
  [TileType.Flowers]: 'var(--tile-flowers)',
  [TileType.Seasons]: 'var(--tile-seasons)',
} as const;

export const BOARD_LAYOUT = {
  TURTLE: {
    name: 'Turtle' as const,
    layers: [
      // Layer 0 (bottom)
      [
        '    XX    ',
        '  XXXXXX  ',
        ' XXXXXXXX ',
        'XXXXXXXXXX',
        'XXXXXXXXXX',
        'XXXXXXXXXX',
        ' XXXXXXXX ',
        '  XXXXXX  ',
        '    XX    ',
      ],
      // Layer 1
      [
        '          ',
        '   XXXX   ',
        '  XXXXXX  ',
        '  XXXXXX  ',
        '  XXXXXX  ',
        '  XXXXXX  ',
        '  XXXXXX  ',
        '   XXXX   ',
        '          ',
      ],
      // Layer 2
      [
        '          ',
        '          ',
        '   XXXX   ',
        '   XXXX   ',
        '   XXXX   ',
        '   XXXX   ',
        '   XXXX   ',
        '          ',
        '          ',
      ],
      // Layer 3
      [
        '          ',
        '          ',
        '          ',
        '    XX    ',
        '    XX    ',
        '    XX    ',
        '          ',
        '          ',
        '          ',
      ],
      // Layer 4 (top)
      [
        '          ',
        '          ',
        '          ',
        '          ',
        '     X    ',
        '          ',
        '          ',
        '          ',
        '          ',
      ],
    ],
  },
};

export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
} as const;