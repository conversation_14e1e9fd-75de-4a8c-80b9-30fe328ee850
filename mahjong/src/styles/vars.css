:root {
  /* Colors - Clean, bold, bright design */
  --primary: #ff6b35;
  --primary-hover: #e55a2b;
  --secondary: #2a9fd6;
  --secondary-hover: #2189bd;
  --accent: #ffd700;
  --accent-hover: #e6c200;
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  
  /* Neutrals */
  --white: #ffffff;
  --black: #1a1a1a;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  
  /* Tile colors - Traditional mahjong inspired */
  --tile-bamboo: #22c55e;
  --tile-dots: #3b82f6;
  --tile-characters: #dc2626;
  --tile-winds: #7c3aed;
  --tile-dragons: #ea580c;
  --tile-flowers: #ec4899;
  --tile-seasons: #84cc16;
  
  /* Surface colors */
  --surface-primary: var(--white);
  --surface-secondary: var(--gray-100);
  --surface-tertiary: var(--gray-200);
  --surface-inverse: var(--black);
  
  /* Border and outline */
  --border-color: var(--gray-300);
  --border-focus: var(--primary);
  --border-width: 2px;
  --border-radius: 8px;
  --border-radius-small: 4px;
  --border-radius-large: 12px;
  
  /* Typography */
  --font-family-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  
  /* Animation */
  --animation-duration-fast: 0.15s;
  --animation-duration: 0.25s;
  --animation-duration-slow: 0.5s;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Game-specific */
  --tile-size: 50px;
  --tile-size-mobile: 40px;
  --tile-depth: 4px;
  --board-padding: var(--space-6);
  --board-gap: var(--space-1);
  
  /* Z-index layers */
  --layer-base: 10;
  --layer-elevated: 20;
  --layer-overlay: 30;
  --layer-modal: 40;
  --layer-tooltip: 50;
  
  /* Breakpoints (for use in JS) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --surface-primary: var(--gray-900);
    --surface-secondary: var(--gray-800);
    --surface-tertiary: var(--gray-700);
    --surface-inverse: var(--white);
    --border-color: var(--gray-600);
  }
}