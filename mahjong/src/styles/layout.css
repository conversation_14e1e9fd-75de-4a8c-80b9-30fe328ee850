/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: var(--line-height-normal);
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--black);
  background-color: var(--surface-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Layout utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: var(--space-2);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.gap-8 {
  gap: var(--space-8);
}

/* Grid utilities */
.grid {
  display: grid;
}

.grid-center {
  place-items: center;
}

/* Spacing utilities */
.p-4 {
  padding: var(--space-4);
}

.p-6 {
  padding: var(--space-6);
}

.p-8 {
  padding: var(--space-8);
}

.m-4 {
  margin: var(--space-4);
}

.m-6 {
  margin: var(--space-6);
}

.m-8 {
  margin: var(--space-8);
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Text utilities */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Size utilities */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

/* Interactive states */
.interactive {
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing);
}

@media (hover: hover) {
  .interactive:hover {
    transform: translateY(-1px);
  }
}

.interactive:active {
  transform: translateY(0);
}

/* Focus styles */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Touch targets */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.fade-in {
  animation: fadeIn var(--animation-duration) var(--animation-easing);
}

.slide-up {
  animation: slideUp var(--animation-duration) var(--animation-easing);
}

.bounce {
  animation: bounce var(--animation-duration-slow) var(--animation-easing-bounce);
}

/* Responsive breakpoints */
@media (max-width: 639px) {
  .sm-hidden {
    display: none;
  }
}

@media (min-width: 640px) {
  .sm-block {
    display: block;
  }
  
  .sm-flex {
    display: flex;
  }
}

@media (min-width: 768px) {
  .md-block {
    display: block;
  }
  
  .md-flex {
    display: flex;
  }
}

@media (min-width: 1024px) {
  .lg-block {
    display: block;
  }
  
  .lg-flex {
    display: flex;
  }
}