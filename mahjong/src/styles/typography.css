/* Typography styles */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

/* Font weights */
.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

/* Line heights */
.leading-tight {
  line-height: var(--line-height-tight);
}

.leading-normal {
  line-height: var(--line-height-normal);
}

.leading-relaxed {
  line-height: var(--line-height-relaxed);
}

/* Headings */
h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-6);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-5);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-4);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-3);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-2);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-2);
}

/* Paragraph and text */
p {
  margin-bottom: var(--space-4);
}

.lead {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--gray-600);
}

.small {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* Links */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--animation-duration) var(--animation-easing);
}

@media (hover: hover) {
  a:hover {
    color: var(--primary-hover);
  }
}

a:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Code */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--gray-100);
  padding: 0.125em 0.25em;
  border-radius: var(--border-radius-small);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--gray-100);
  padding: var(--space-4);
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin-bottom: var(--space-4);
}

pre code {
  background-color: transparent;
  padding: 0;
}

/* Lists */
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-1);
}

/* Responsive typography */
@media (max-width: 767px) {
  h1 {
    font-size: var(--font-size-3xl);
  }
  
  h2 {
    font-size: var(--font-size-2xl);
  }
  
  h3 {
    font-size: var(--font-size-xl);
  }
}

/* Game-specific typography */
.game-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--primary);
  text-align: center;
  margin-bottom: var(--space-8);
}

@media (max-width: 767px) {
  .game-title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-6);
  }
}

.tile-symbol {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

@media (max-width: 767px) {
  .tile-symbol {
    font-size: var(--font-size-base);
  }
}