'use client';

import { useEffect, memo } from 'react';
import { useGame } from '../../context/GameContext';
import { GameStatus, Difficulty } from '../../types/game';
import GameBoard from './GameBoard';
import styles from './Game.module.css';

export default memo(function Game() {
  const { gameState, gameStats, startGame, selectTile, resetGame } = useGame();
  
  console.log('Game state:', gameState);
  console.log('Total tiles:', gameState.tiles.length);
  console.log('Available tiles:', gameState.tiles.filter(t => !t.isBlocked && t.state === 'available').length);

  // Initialize game on mount
  useEffect(() => {
    if (gameState.status === GameStatus.NotStarted) {
      startGame(Difficulty.Medium);
    }
  }, [gameState.status, startGame]);


  const handleTileClick = (tile: any) => {
    console.log('Tile clicked:', tile);
    console.log('Is blocked:', tile.isBlocked);
    console.log('Tile state:', tile.state);
    selectTile(tile);
  };

  const handleNewGame = () => {
    resetGame();
    startGame(gameState.difficulty);
  };

  return (
    <div className={styles.game}>
      <header className={styles.gameHeader}>
        <h1 className="game-title">Mahjong Solitaire</h1>
        
        <div className={styles.gameInfo}>
          <div className={styles.stat}>
            <span className={styles.statLabel}>Tiles</span>
            <span className={styles.statValue}>{gameStats.tilesRemaining}</span>
          </div>
          
          <div className={styles.stat}>
            <span className={styles.statLabel}>Matches</span>
            <span className={styles.statValue}>{gameStats.matchesFound}</span>
          </div>
          
          <div className={styles.stat}>
            <span className={styles.statLabel}>Score</span>
            <span className={styles.statValue}>{gameState.score}</span>
          </div>
          
          {gameState.startTime && (
            <div className={styles.stat}>
              <span className={styles.statLabel}>Time</span>
              <span className={styles.statValue}>
                {Math.floor(gameStats.elapsedTime / 60000)}:
                {String(Math.floor((gameStats.elapsedTime % 60000) / 1000)).padStart(2, '0')}
              </span>
            </div>
          )}
        </div>

        <div className={styles.gameActions}>
          <button 
            className={styles.actionButton}
            onClick={handleNewGame}
            type="button"
          >
            New Game
          </button>
        </div>
      </header>

      <main className={styles.gameMain}>
        {gameState.status === GameStatus.Won && (
          <div className={styles.gameMessage}>
            <h2>🎉 Congratulations!</h2>
            <p>You completed the puzzle in {Math.floor(gameStats.elapsedTime / 1000)} seconds!</p>
            <button 
              className={styles.actionButton}
              onClick={handleNewGame}
              type="button"
            >
              Play Again
            </button>
          </div>
        )}

        {gameState.status === GameStatus.Lost && (
          <div className={styles.gameMessage}>
            <h2>Game Over</h2>
            <p>No more moves available. Try again!</p>
            <button 
              className={styles.actionButton}
              onClick={handleNewGame}
              type="button"
            >
              Try Again
            </button>
          </div>
        )}

        {gameState.tiles.length > 0 && (
          <GameBoard 
            tiles={gameState.tiles}
            onTileClick={handleTileClick}
          />
        )}
      </main>
    </div>
  );
});