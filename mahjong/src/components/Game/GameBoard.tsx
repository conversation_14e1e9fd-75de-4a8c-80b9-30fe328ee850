'use client';

import { memo } from 'react';
import classNames from 'classnames';
import type { Tile as TileType } from '../../types/tile';
import Tile from '../Tile/Tile';
import styles from './GameBoard.module.css';

type GameBoardProps = {
  tiles: TileType[];
  onTileClick?: (tile: TileType) => void;
  className?: string;
};

export default memo(function GameBoard({ tiles, onTileClick, className }: GameBoardProps) {
  // Group tiles by layer for proper rendering order
  const tilesByLayer = tiles.reduce((acc, tile) => {
    const layer = tile.position.layer;
    if (!acc[layer]) {
      acc[layer] = [];
    }
    acc[layer].push(tile);
    return acc;
  }, {} as Record<number, TileType[]>);

  const layers = Object.keys(tilesByLayer)
    .map(Number)
    .sort((a, b) => a - b);

  return (
    <div className={classNames(styles.gameBoard, className)}>
      <div className={styles.boardContainer}>
        {layers.map(layer => (
          <div 
            key={layer}
            className={styles.layer}
            style={{
              '--layer-index': layer,
              zIndex: layer + 1,
            } as React.CSSProperties}
          >
            {tilesByLayer[layer].map(tile => (
              <div
                key={tile.id}
                className={styles.tilePosition}
                style={{
                  '--tile-row': tile.position.row,
                  '--tile-col': tile.position.col,
                } as React.CSSProperties}
              >
                <Tile
                  tile={tile}
                  onClick={onTileClick}
                />
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
});