.gameBoard {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  padding: var(--board-padding);
  overflow: visible;
  perspective: 1000px;
  width: 100%;
}

@media (max-width: 767px) {
  .gameBoard {
    min-height: 300px;
    padding: var(--space-4);
  }
}

.layer {
  position: absolute;
  display: grid;
  grid-template-columns: repeat(10, var(--tile-size));
  grid-template-rows: repeat(9, var(--tile-size));
  gap: var(--board-gap);
  transform-style: preserve-3d;
  
  /* Each layer is slightly offset for 3D effect */
  transform: translateZ(calc(var(--layer-index, 0) * 3px));
  
  /* Ensure the layer takes up proper space */
  width: calc(10 * var(--tile-size) + 9 * var(--board-gap));
  height: calc(9 * var(--tile-size) + 8 * var(--board-gap));
}

@media (max-width: 767px) {
  .layer {
    grid-template-columns: repeat(10, var(--tile-size-mobile));
    grid-template-rows: repeat(9, var(--tile-size-mobile));
    gap: calc(var(--board-gap) / 2);
    width: calc(10 * var(--tile-size-mobile) + 9 * calc(var(--board-gap) / 2));
    height: calc(9 * var(--tile-size-mobile) + 8 * calc(var(--board-gap) / 2));
  }
}

.tilePosition {
  grid-row: calc(var(--tile-row, 0) + 1);
  grid-column: calc(var(--tile-col, 0) + 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive board scaling - more conservative scaling */
@media (max-width: 1200px) {
  .gameBoard {
    scale: 0.95;
  }
}

@media (max-width: 1024px) {
  .gameBoard {
    scale: 0.9;
  }
}

@media (max-width: 768px) {
  .gameBoard {
    scale: 0.85;
    min-height: 400px;
  }
}

@media (max-width: 640px) {
  .gameBoard {
    scale: 0.8;
    min-height: 350px;
  }
}

@media (max-width: 480px) {
  .gameBoard {
    scale: 0.75;
    min-height: 300px;
  }
}

/* Smooth scaling transition */
.gameBoard {
  transition: scale var(--animation-duration) var(--animation-easing);
}

.boardContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
}

/* Board background with subtle pattern */
.gameBoard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, var(--gray-100) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, var(--gray-100) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
  pointer-events: none;
  z-index: -1;
}

/* Focus management for keyboard navigation */
.gameBoard:focus-within {
  outline: 2px solid var(--secondary);
  outline-offset: 4px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .gameBoard::after {
    display: none;
  }
}

/* Print styles */
@media print {
  .gameBoard {
    scale: 1;
    perspective: none;
  }
  
  .layer {
    transform: none;
  }
  
  .gameBoard::after {
    display: none;
  }
}