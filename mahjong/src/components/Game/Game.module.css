.game {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--white) 100%);
  padding: var(--space-4);
}

@media (min-width: 768px) {
  .game {
    padding: var(--space-8);
  }
}

.gameHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .gameHeader {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-8);
  }
}

.gameInfo {
  display: flex;
  gap: var(--space-6);
  flex-wrap: wrap;
  justify-content: center;
}

@media (min-width: 768px) {
  .gameInfo {
    justify-content: flex-start;
  }
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 60px;
}

.statLabel {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-1);
}

.statValue {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary);
  line-height: 1;
}

.gameActions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
  justify-content: center;
}

@media (min-width: 768px) {
  .gameActions {
    justify-content: flex-end;
  }
}

.actionButton {
  padding: var(--space-3) var(--space-6);
  background: var(--primary);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing);
  min-height: 44px;
  min-width: 100px;
}

@media (hover: hover) {
  .actionButton:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.actionButton:active {
  transform: translateY(0);
  box-shadow: var(--shadow);
}

.actionButton:focus-visible {
  outline: 2px solid var(--secondary);
  outline-offset: 2px;
}

.gameMain {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gameInstructions {
  background: var(--surface-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  max-width: 600px;
  text-align: center;
}

.gameInstructions h3 {
  margin-bottom: var(--space-2);
  color: var(--primary);
}

.gameInstructions p {
  margin-bottom: 0;
  line-height: var(--line-height-relaxed);
  color: var(--gray-700);
}

.gameMessage {
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-large);
  padding: var(--space-8);
  text-align: center;
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-8);
  max-width: 400px;
  animation: slideUp var(--animation-duration) var(--animation-easing);
}

.gameMessage h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary);
  margin-bottom: var(--space-4);
}

.gameMessage p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  margin-bottom: var(--space-6);
}

@media (max-width: 640px) {
  .gameMessage {
    margin-left: var(--space-4);
    margin-right: var(--space-4);
    padding: var(--space-6);
  }
  
  .gameMessage h2 {
    font-size: var(--font-size-2xl);
  }
  
  .gameMessage p {
    font-size: var(--font-size-base);
  }
}

/* Loading state */
.gameLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--space-4);
}

.gameLoading::before {
  content: '';
  width: 40px;
  height: 40px;
  border: 3px solid var(--gray-200);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error state */
.gameError {
  background: var(--error);
  color: var(--white);
  padding: var(--space-4);
  border-radius: var(--border-radius);
  text-align: center;
  margin-bottom: var(--space-6);
}

/* Game status indicators */
.gameStatus {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  z-index: var(--layer-overlay);
}

.gameStatus.playing {
  background: var(--success);
  color: var(--white);
}

.gameStatus.paused {
  background: var(--warning);
  color: var(--black);
}

/* Responsive layout adjustments */
@media (max-width: 640px) {
  .gameHeader {
    gap: var(--space-4);
  }
  
  .gameInfo {
    gap: var(--space-4);
  }
  
  .stat {
    min-width: 50px;
  }
  
  .statLabel {
    font-size: var(--font-size-xs);
  }
  
  .statValue {
    font-size: var(--font-size-lg);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .actionButton {
    border: 2px solid var(--primary);
  }
  
  .gameMessage {
    border-width: 3px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .gameMessage {
    animation: none;
  }
  
  .actionButton {
    transition: none;
  }
  
  .actionButton:hover {
    transform: none;
  }
}