.tile {
  position: relative;
  width: var(--tile-size);
  height: var(--tile-size);
  background: var(--surface-primary);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing);
  box-shadow: var(--shadow), 
              0 var(--tile-depth) 0 var(--gray-300),
              0 var(--tile-depth) var(--shadow);
  user-select: none;
  overflow: hidden;
  
  /* 3D effect with layer offset */
  transform: translate3d(
    calc(var(--layer-offset) * -0.5), 
    calc(var(--layer-offset) * -0.5), 
    0
  );
}

@media (max-width: 767px) {
  .tile {
    width: var(--tile-size-mobile);
    height: var(--tile-size-mobile);
  }
}

.tile:disabled {
  cursor: not-allowed;
}

.tileContent {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--white) 0%, var(--gray-100) 100%);
  z-index: 1;
}

.tileSymbol {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
  color: var(--tile-color, var(--black));
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
  .tileSymbol {
    font-size: var(--font-size-lg);
  }
}

/* Tile states */
.tile.available {
  border-color: var(--border-color);
}

.tile.available.interactive {
  cursor: pointer;
}

@media (hover: hover) {
  .tile.available.interactive:hover {
    transform: translate3d(
      calc(var(--layer-offset) * -0.5), 
      calc(var(--layer-offset) * -0.5 - 2px), 
      0
    );
    box-shadow: var(--shadow-md), 
                0 calc(var(--tile-depth) + 2px) 0 var(--gray-300),
                0 calc(var(--tile-depth) + 2px) var(--shadow-md);
  }
}

.tile.available.interactive:active {
  transform: translate3d(
    calc(var(--layer-offset) * -0.5), 
    calc(var(--layer-offset) * -0.5 + 1px), 
    0
  );
  box-shadow: var(--shadow), 
              0 calc(var(--tile-depth) - 1px) 0 var(--gray-300),
              0 calc(var(--tile-depth) - 1px) var(--shadow);
}

.tile.selected {
  border-color: var(--primary);
  border-width: 3px;
  box-shadow: var(--shadow-md), 
              0 0 0 2px var(--primary),
              0 var(--tile-depth) 0 var(--gray-300),
              0 var(--tile-depth) var(--shadow-md);
  transform: translate3d(
    calc(var(--layer-offset) * -0.5), 
    calc(var(--layer-offset) * -0.5 - 3px), 
    0
  );
}

.tile.selected .tileContent {
  background: linear-gradient(135deg, var(--white) 0%, var(--accent) 20%, var(--white) 100%);
}

.tile.matched {
  opacity: 0;
  transform: translate3d(
    calc(var(--layer-offset) * -0.5), 
    calc(var(--layer-offset) * -0.5 - 20px), 
    0
  ) scale(0.8);
  pointer-events: none;
  transition: all var(--animation-duration-slow) var(--animation-easing);
}

.tile.hidden {
  opacity: 0.3;
  cursor: not-allowed;
  filter: grayscale(0.8);
}

.tile.hidden .tileContent {
  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);
}

.tile.hidden .tileSymbol {
  color: var(--gray-500);
}

/* Focus styles for accessibility */
.tile:focus-visible {
  outline: 3px solid var(--secondary);
  outline-offset: 2px;
}

/* Animation for tile entrance */
@starting-style {
  .tile {
    opacity: 0;
    transform: translate3d(
      calc(var(--layer-offset) * -0.5), 
      calc(var(--layer-offset) * -0.5 + 20px), 
      0
    ) scale(0.8);
  }
}

/* Tile type specific styling */
.tile[data-type="dots"] {
  --tile-color: var(--tile-dots);
}

.tile[data-type="bamboo"] {
  --tile-color: var(--tile-bamboo);
}

.tile[data-type="characters"] {
  --tile-color: var(--tile-characters);
}

.tile[data-type="winds"] {
  --tile-color: var(--tile-winds);
}

.tile[data-type="dragons"] {
  --tile-color: var(--tile-dragons);
}

.tile[data-type="flowers"] {
  --tile-color: var(--tile-flowers);
}

.tile[data-type="seasons"] {
  --tile-color: var(--tile-seasons);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tile {
    border-width: 3px;
  }
  
  .tile.selected {
    border-width: 4px;
  }
  
  .tileSymbol {
    font-weight: var(--font-weight-bold);
    text-shadow: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tile {
    transition: none;
  }
  
  .tile.matched {
    transition: opacity var(--animation-duration) ease;
  }
  
  @starting-style {
    .tile {
      transform: translate3d(
        calc(var(--layer-offset) * -0.5), 
        calc(var(--layer-offset) * -0.5), 
        0
      );
    }
  }
}