'use client';

import { memo } from 'react';
import classNames from 'classnames';
import type { Tile as TileType } from '../../types/tile';
import { TileState } from '../../types/tile';
import { TILE_COLORS } from '../../config/constants';
import styles from './Tile.module.css';

type TileProps = {
  tile: TileType;
  onClick?: (tile: TileType) => void;
  className?: string;
};

export default memo(function Tile({ tile, onClick, className }: TileProps) {
  const handleClick = () => {
    if (onClick && !tile.isBlocked && tile.state !== TileState.Matched) {
      onClick(tile);
    }
  };

  const isInteractive = !tile.isBlocked && tile.state !== TileState.Matched;
  const tileColor = TILE_COLORS[tile.type];

  return (
    <button
      className={classNames(
        styles.tile,
        {
          [styles.available]: tile.state === TileState.Available,
          [styles.selected]: tile.state === TileState.Selected,
          [styles.matched]: tile.state === TileState.Matched,
          [styles.hidden]: tile.state === TileState.Hidden || tile.isBlocked,
          [styles.interactive]: isInteractive,
        },
        className
      )}
      onClick={handleClick}
      disabled={!isInteractive}
      style={{
        '--tile-color': tileColor,
        '--layer-offset': `${tile.position.layer * 2}px`,
      } as React.CSSProperties}
      aria-label={`${tile.type} ${tile.value} tile`}
      data-type={tile.type}
      data-blocked={tile.isBlocked}
      data-state={tile.state}
      type="button"
    >
      <div className={styles.tileContent}>
        <div className={styles.tileSymbol}>
          {tile.symbol}
        </div>
      </div>
    </button>
  );
});