'use client';

import { createContext, useContext, useReducer, useMemo, useCallback, ReactNode } from 'react';
import type { GameState, GameAction, GameStats } from '../types/game';
import { Difficulty, GameStatus } from '../types/game';
import type { Tile } from '../types/tile';
import { TileState } from '../types/tile';
import { generateGameTiles, updateTileBlocking } from '../utils/tileGeneration';

interface GameContextType {
  gameState: GameState;
  gameStats: GameStats;
  startGame: (difficulty: Difficulty) => void;
  selectTile: (tile: Tile) => void;
  deselectTile: () => void;
  useHint: () => void;
  shuffleTiles: () => void;
  pauseGame: () => void;
  resumeGame: () => void;
  resetGame: () => void;
}

const GameContext = createContext<GameContextType | null>(null);

const initialGameState: GameState = {
  tiles: [],
  selectedTile: null,
  matches: [],
  status: GameStatus.NotStarted,
  difficulty: Difficulty.Medium,
  startTime: null,
  endTime: null,
  score: 0,
  hintsUsed: 0,
  shufflesUsed: 0,
};

function gameReducer(state: GameState, action: GameAction): GameState {
  switch (action.type) {
    case 'START_GAME': {
      const tiles = generateGameTiles('TURTLE');
      return {
        ...initialGameState,
        status: GameStatus.Playing,
        difficulty: action.payload.difficulty,
        startTime: Date.now(),
        tiles,
      };
    }

    case 'SELECT_TILE': {
      const { tile } = action.payload;
      
      // If no tile is currently selected, select this one
      if (!state.selectedTile) {
        return {
          ...state,
          selectedTile: tile,
          tiles: state.tiles.map(t => 
            t.id === tile.id 
              ? { ...t, state: TileState.Selected }
              : { ...t, state: t.state === TileState.Selected ? TileState.Available : t.state }
          ),
        };
      }

      // If the same tile is clicked, deselect it
      if (state.selectedTile.id === tile.id) {
        return {
          ...state,
          selectedTile: null,
          tiles: state.tiles.map(t => 
            t.id === tile.id 
              ? { ...t, state: TileState.Available }
              : t
          ),
        };
      }

      // If a different tile is clicked, check for match
      const canMatch = tilesMatch(state.selectedTile, tile);
      
      if (canMatch) {
        const newMatch = {
          tile1: state.selectedTile,
          tile2: tile,
          timestamp: Date.now(),
        };

        let updatedTiles = state.tiles.map(t => {
          if (t.id === state.selectedTile!.id || t.id === tile.id) {
            return { ...t, state: TileState.Matched };
          }
          return t;
        });

        // Update tile blocking after match
        updatedTiles = updateTileBlocking(updatedTiles);

        // Check if game is won
        const remainingTiles = updatedTiles.filter(t => t.state !== TileState.Matched);
        const gameWon = remainingTiles.length === 0;

        return {
          ...state,
          selectedTile: null,
          matches: [...state.matches, newMatch],
          tiles: updatedTiles,
          score: state.score + 10,
          status: gameWon ? GameStatus.Won : state.status,
          endTime: gameWon ? Date.now() : state.endTime,
        };
      } else {
        // Select the new tile
        return {
          ...state,
          selectedTile: tile,
          tiles: state.tiles.map(t => {
            if (t.id === tile.id) {
              return { ...t, state: TileState.Selected };
            }
            if (t.id === state.selectedTile!.id) {
              return { ...t, state: TileState.Available };
            }
            return t;
          }),
        };
      }
    }

    case 'DESELECT_TILE':
      return {
        ...state,
        selectedTile: null,
        tiles: state.tiles.map(t => 
          t.state === TileState.Selected 
            ? { ...t, state: TileState.Available }
            : t
        ),
      };

    case 'USE_HINT':
      return {
        ...state,
        hintsUsed: state.hintsUsed + 1,
        score: Math.max(0, state.score - 5),
      };

    case 'SHUFFLE_TILES':
      return {
        ...state,
        shufflesUsed: state.shufflesUsed + 1,
        score: Math.max(0, state.score - 10),
        selectedTile: null,
        tiles: state.tiles.map(t => 
          t.state === TileState.Selected 
            ? { ...t, state: TileState.Available }
            : t
        ),
      };

    case 'PAUSE_GAME':
      return {
        ...state,
        status: GameStatus.Paused,
      };

    case 'RESUME_GAME':
      return {
        ...state,
        status: GameStatus.Playing,
      };

    case 'END_GAME':
      return {
        ...state,
        status: action.payload.status,
        endTime: Date.now(),
        selectedTile: null,
      };

    case 'RESET_GAME':
      return initialGameState;

    default:
      return state;
  }
}

function tilesMatch(tile1: Tile, tile2: Tile): boolean {
  // Flowers and Seasons match with any tile of the same type
  if (tile1.type === 'flowers' || tile1.type === 'seasons') {
    return tile1.type === tile2.type;
  }
  
  // All other tiles must match exactly
  return tile1.type === tile2.type && tile1.value === tile2.value;
}

interface GameProviderProps {
  children: ReactNode;
}

export function GameProvider({ children }: GameProviderProps) {
  const [gameState, dispatch] = useReducer(gameReducer, initialGameState);

  const gameStats = useMemo((): GameStats => {
    const tilesRemaining = gameState.tiles.filter(t => t.state !== TileState.Matched).length;
    const matchesFound = gameState.matches.length;
    const elapsedTime = gameState.startTime 
      ? (gameState.endTime || Date.now()) - gameState.startTime
      : 0;
    
    // Calculate available moves (simplified)
    const availableTiles = gameState.tiles.filter(t => 
      t.state !== TileState.Matched && !t.isBlocked
    );
    let availableMoves = 0;
    
    for (let i = 0; i < availableTiles.length; i++) {
      for (let j = i + 1; j < availableTiles.length; j++) {
        if (tilesMatch(availableTiles[i], availableTiles[j])) {
          availableMoves++;
        }
      }
    }

    return {
      tilesRemaining,
      matchesFound,
      elapsedTime,
      availableMoves,
    };
  }, [gameState]);

  const startGame = useCallback((difficulty: Difficulty) => {
    dispatch({ type: 'START_GAME', payload: { difficulty } });
  }, []);

  const selectTile = useCallback((tile: Tile) => {
    if (gameState.status !== GameStatus.Playing) return;
    if (tile.isBlocked || tile.state === TileState.Matched) return;
    
    dispatch({ type: 'SELECT_TILE', payload: { tile } });
  }, [gameState.status]);

  const deselectTile = useCallback(() => {
    dispatch({ type: 'DESELECT_TILE' });
  }, []);

  const useHint = useCallback(() => {
    if (gameState.hintsUsed >= 3) return;
    dispatch({ type: 'USE_HINT' });
  }, [gameState.hintsUsed]);

  const shuffleTiles = useCallback(() => {
    if (gameState.shufflesUsed >= 2) return;
    dispatch({ type: 'SHUFFLE_TILES' });
  }, [gameState.shufflesUsed]);

  const pauseGame = useCallback(() => {
    dispatch({ type: 'PAUSE_GAME' });
  }, []);

  const resumeGame = useCallback(() => {
    dispatch({ type: 'RESUME_GAME' });
  }, []);

  const resetGame = useCallback(() => {
    dispatch({ type: 'RESET_GAME' });
  }, []);

  const contextValue = useMemo(() => ({
    gameState,
    gameStats,
    startGame,
    selectTile,
    deselectTile,
    useHint,
    shuffleTiles,
    pauseGame,
    resumeGame,
    resetGame,
  }), [
    gameState,
    gameStats,
    startGame,
    selectTile,
    deselectTile,
    useHint,
    shuffleTiles,
    pauseGame,
    resumeGame,
    resetGame,
  ]);

  return (
    <GameContext.Provider value={contextValue}>
      {children}
    </GameContext.Provider>
  );
}

export function useGame(): GameContextType {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
}