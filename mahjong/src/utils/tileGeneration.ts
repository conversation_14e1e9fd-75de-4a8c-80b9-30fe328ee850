import type { Tile, Position } from '../types/tile';
import { TileType, TileState } from '../types/tile';
import { TILE_SYMBOLS, BOARD_LAYOUT } from '../config/constants';

function generateTileId(): string {
  return `tile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function createTileSet(): Omit<Tile, 'id' | 'position' | 'state' | 'isBlocked'>[] {
  const tiles: Omit<Tile, 'id' | 'position' | 'state' | 'isBlocked'>[] = [];

  // Dots (1-9, 4 of each)
  for (let value = 1; value <= 9; value++) {
    for (let i = 0; i < 4; i++) {
      tiles.push({
        type: TileType.Dots,
        value,
        symbol: TILE_SYMBOLS[TileType.Dots][value - 1],
      });
    }
  }

  // Bamboo (1-9, 4 of each)  
  for (let value = 1; value <= 9; value++) {
    for (let i = 0; i < 4; i++) {
      tiles.push({
        type: TileType.Bamboo,
        value,
        symbol: TILE_SYMBOLS[TileType.Bamboo][value - 1],
      });
    }
  }

  // Characters (1-9, 4 of each)
  for (let value = 1; value <= 9; value++) {
    for (let i = 0; i < 4; i++) {
      tiles.push({
        type: TileType.Characters,
        value,
        symbol: TILE_SYMBOLS[TileType.Characters][value - 1],
      });
    }
  }

  // Winds (4 types, 4 of each)
  TILE_SYMBOLS[TileType.Winds].forEach((symbol, index) => {
    for (let i = 0; i < 4; i++) {
      tiles.push({
        type: TileType.Winds,
        value: index + 1,
        symbol,
      });
    }
  });

  // Dragons (3 types, 4 of each)
  TILE_SYMBOLS[TileType.Dragons].forEach((symbol, index) => {
    for (let i = 0; i < 4; i++) {
      tiles.push({
        type: TileType.Dragons,
        value: index + 1,
        symbol,
      });
    }
  });

  // Flowers (4 unique)
  TILE_SYMBOLS[TileType.Flowers].forEach((symbol, index) => {
    tiles.push({
      type: TileType.Flowers,
      value: index + 1,
      symbol,
    });
  });

  // Seasons (4 unique)
  TILE_SYMBOLS[TileType.Seasons].forEach((symbol, index) => {
    tiles.push({
      type: TileType.Seasons,
      value: index + 1,
      symbol,
    });
  });

  return tiles;
}

function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

function getPositionsFromLayout(layout: string[][]): Position[] {
  const positions: Position[] = [];
  
  layout.forEach((layer, layerIndex) => {
    layer.forEach((row, rowIndex) => {
      for (let colIndex = 0; colIndex < row.length; colIndex++) {
        if (row[colIndex] === 'X') {
          positions.push({
            row: rowIndex,
            col: colIndex,
            layer: layerIndex,
          });
        }
      }
    });
  });
  
  return positions;
}

function isTileBlocked(position: Position, allPositions: Position[]): boolean {
  const { row, col, layer } = position;
  
  // Check if there's a tile above
  const hasAbove = allPositions.some(pos => 
    pos.row === row && pos.col === col && pos.layer === layer + 1
  );
  
  if (hasAbove) return true;
  
  // Check if both left and right sides are blocked
  const hasLeft = allPositions.some(pos =>
    pos.layer === layer && pos.row === row && pos.col === col - 1
  );
  
  const hasRight = allPositions.some(pos =>
    pos.layer === layer && pos.row === row && pos.col === col + 1
  );
  
  return hasLeft && hasRight;
}

export function generateGameTiles(layoutName: keyof typeof BOARD_LAYOUT = 'TURTLE'): Tile[] {
  const layout = BOARD_LAYOUT[layoutName].layers;
  const positions = getPositionsFromLayout(layout);
  const tileSet = createTileSet();
  
  // Ensure we have enough tiles for the layout
  const neededTiles = positions.length;
  const availableTiles = [...tileSet];
  
  // If we need more tiles, duplicate some randomly
  while (availableTiles.length < neededTiles) {
    const randomTile = tileSet[Math.floor(Math.random() * tileSet.length)];
    availableTiles.push(randomTile);
  }
  
  // Shuffle tiles and take only what we need
  const shuffledTiles = shuffleArray(availableTiles).slice(0, neededTiles);
  
  // Create final tiles with positions
  const tiles: Tile[] = shuffledTiles.map((tileData, index) => {
    const position = positions[index];
    const isBlocked = isTileBlocked(position, positions);
    
    console.log(`Tile at (${position.row}, ${position.col}, layer ${position.layer}): blocked=${isBlocked}`);
    
    return {
      id: generateTileId(),
      ...tileData,
      position,
      state: isBlocked ? TileState.Hidden : TileState.Available,
      isBlocked,
    };
  });
  
  return tiles;
}

export function updateTileBlocking(tiles: Tile[]): Tile[] {
  const activeTiles = tiles.filter(tile => tile.state !== TileState.Matched);
  const activePositions = activeTiles.map(tile => tile.position);
  
  return tiles.map(tile => {
    if (tile.state === TileState.Matched) return tile;
    
    const isBlocked = isTileBlocked(tile.position, activePositions);
    const newState = isBlocked ? TileState.Hidden : TileState.Available;
    
    return {
      ...tile,
      isBlocked,
      state: tile.state === TileState.Selected ? tile.state : newState,
    };
  });
}

export function findAvailableMatches(tiles: Tile[]): [Tile, Tile][] {
  const availableTiles = tiles.filter(tile => 
    !tile.isBlocked && tile.state !== TileState.Matched
  );
  
  const matches: [Tile, Tile][] = [];
  
  for (let i = 0; i < availableTiles.length; i++) {
    for (let j = i + 1; j < availableTiles.length; j++) {
      const tile1 = availableTiles[i];
      const tile2 = availableTiles[j];
      
      if (tilesMatch(tile1, tile2)) {
        matches.push([tile1, tile2]);
      }
    }
  }
  
  return matches;
}

function tilesMatch(tile1: Tile, tile2: Tile): boolean {
  // Flowers and Seasons match with any tile of the same type
  if (tile1.type === TileType.Flowers || tile1.type === TileType.Seasons) {
    return tile1.type === tile2.type;
  }
  
  // All other tiles must match exactly
  return tile1.type === tile2.type && tile1.value === tile2.value;
}