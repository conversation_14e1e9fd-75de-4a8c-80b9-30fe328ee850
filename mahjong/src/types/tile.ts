export enum TileType {
  Dots = 'dots',
  Bamboo = 'bamboo', 
  Characters = 'characters',
  Winds = 'winds',
  Dragons = 'dragons',
  Flowers = 'flowers',
  Seasons = 'seasons',
}

export enum TileState {
  Default = 'default',
  Available = 'available',
  Selected = 'selected',
  Matched = 'matched',
  Hidden = 'hidden',
}

export interface Position {
  row: number;
  col: number;
  layer: number;
}

export interface Tile {
  id: string;
  type: TileType;
  value: number | string;
  position: Position;
  state: TileState;
  isBlocked: boolean;
  symbol: string;
}

export type TileMatch = {
  tile1: Tile;
  tile2: Tile;
  timestamp: number;
};