import type { Tile, TileMatch } from './tile';

export enum GameStatus {
  NotStarted = 'not-started',
  Playing = 'playing',
  Won = 'won',
  Lost = 'lost',
  Paused = 'paused',
}

export enum Difficulty {
  Easy = 'easy',
  Medium = 'medium', 
  Hard = 'hard',
}

export interface GameState {
  tiles: Tile[];
  selectedTile: Tile | null;
  matches: TileMatch[];
  status: GameStatus;
  difficulty: Difficulty;
  startTime: number | null;
  endTime: number | null;
  score: number;
  hintsUsed: number;
  shufflesUsed: number;
}

export interface GameStats {
  tilesRemaining: number;
  matchesFound: number;
  elapsedTime: number;
  availableMoves: number;
}

export interface GameSettings {
  difficulty: Difficulty;
  showHints: boolean;
  allowShuffle: boolean;
  maxHints: number;
  maxShuffles: number;
}

export type GameAction = 
  | { type: 'START_GAME'; payload: { difficulty: Difficulty } }
  | { type: 'SELECT_TILE'; payload: { tile: Tile } }
  | { type: 'DESELECT_TILE' }
  | { type: 'MATCH_TILES'; payload: { tile1: Tile; tile2: Tile } }
  | { type: 'USE_HINT' }
  | { type: 'SHUFFLE_TILES' }
  | { type: 'PAUSE_GAME' }
  | { type: 'RESUME_GAME' }
  | { type: 'END_GAME'; payload: { status: GameStatus } }
  | { type: 'RESET_GAME' };