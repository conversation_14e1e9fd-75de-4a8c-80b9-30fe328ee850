import type { Metadata, Viewport } from 'next';
import { GameProvider } from '../src/context/GameContext';
import '../src/styles/vars.css';
import '../src/styles/layout.css';
import '../src/styles/typography.css';

export const metadata: Metadata = {
  title: 'Mahjong Solitaire',
  description: 'A clean, modern mahjong solitaire game built with Next.js and React',
  keywords: ['mahjong', 'solitaire', 'puzzle', 'game', 'tiles'],
  authors: [{ name: 'Mahjong Solitaire' }],
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#ff6b35',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <GameProvider>
          {children}
        </GameProvider>
      </body>
    </html>
  );
}