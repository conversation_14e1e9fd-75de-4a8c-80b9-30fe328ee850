import { describe, expect, test } from '@jest/globals';
import { Color, State, isColumnSolved, isPuzzleSolved, toPrettyString, toStringStates, canPour, getNumberOfBlocksCanPour, pour, getNextStates, getSortColumns, solvePuzzle } from './solve';
const { simplePuzzle, level2, level21 } = require('./solve');

describe('isColumnSolved', () => {
  test('empty column is solved', () => {
    expect(isColumnSolved([])).toBe(true);
  });
  test('column with one block is not solved', () => {
    expect(isColumnSolved([Color.Orange])).toBe(false);
  });
  test('full column with same color is solved', () => {
    expect(isColumnSolved([Color.Orange, Color.Orange, Color.Orange, Color.Orange])).toBe(true);
  });
  test('full column with different colors is not solved', () => {
    expect(isColumnSolved([Color.Orange, Color.Orange, Color.DarkBlue, Color.Orange])).toBe(false);
  });
});

describe('isPuzzleSolved', () => {
  test('puzzle with one full column is solved', () => {
    expect(isPuzzleSolved([[Color.Orange, Color.Orange, Color.Orange, Color.Orange]])).toBe(true);
  });
  test('puzzle with first column solved second is empty', () => {
    expect(isPuzzleSolved([[Color.Orange, Color.Orange, Color.Orange, Color.Orange], []])).toBe(true);
  });
  test('puzzle with first column empty second is solved', () => {
    expect(isPuzzleSolved([[], [Color.Orange, Color.Orange, Color.Orange, Color.Orange]])).toBe(true);
  });
  test('puzzle2 is not solved', () => {
    expect(isPuzzleSolved(level2)).toBe(false);
  });
});

describe('toPrettyString', () => {
  test('toPrettyString for empty puzzle', () => {
    let puzzle: Color[][] = [[]];
    expect(toPrettyString(puzzle)).toBe(Color.Empty + '\n' + Color.Empty + '\n' + Color.Empty + '\n' + Color.Empty + '\n');
  });
  test('toPrettyString when first column is empty', () => {
    expect(toPrettyString([[], [Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.Orange]])).toBe(
      [Color.Empty, ' ', Color.Orange, '\n'].join('') + [Color.Empty, ' ', Color.DarkBlue, '\n'].join('') + [Color.Empty, ' ', Color.Orange, '\n'].join('') + [Color.Empty, ' ', Color.DarkBlue, '\n'].join(''),
    );
  });
  test('toPrettyString when last column is empty', () => {
    expect(toPrettyString([[Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.Orange], []])).toBe(
      [Color.Orange, ' ', Color.Empty, '\n'].join('') + [Color.DarkBlue, ' ', Color.Empty, '\n'].join('') + [Color.Orange, ' ', Color.Empty, '\n'].join('') + [Color.DarkBlue, ' ', Color.Empty, '\n'].join(''),
    );
  });
  test('toPrettyString when middle column is empty', () => {
    expect(toPrettyString([[Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.Orange], [], [Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.Orange]])).toBe(
      [Color.Orange, ' ', Color.Empty, ' ', Color.Orange, '\n'].join('') +
        [Color.DarkBlue, ' ', Color.Empty, ' ', Color.DarkBlue, '\n'].join('') +
        [Color.Orange, ' ', Color.Empty, ' ', Color.Orange, '\n'].join('') +
        [Color.DarkBlue, ' ', Color.Empty, ' ', Color.DarkBlue, '\n'].join(''),
    );
  });
  test('toPrettyString when every column has only 1 square', () => {
    expect(toPrettyString([[Color.DarkBlue], [Color.Orange], [Color.DarkBlue]])).toBe(
      [Color.Empty, ' ', Color.Empty, ' ', Color.Empty, '\n'].join('') +
        [Color.Empty, ' ', Color.Empty, ' ', Color.Empty, '\n'].join('') +
        [Color.Empty, ' ', Color.Empty, ' ', Color.Empty, '\n'].join('') +
        [Color.DarkBlue, ' ', Color.Orange, ' ', Color.DarkBlue, '\n'].join(''),
    );
  });
  test('toPrettyString for large puzzle', () => {
    expect(toPrettyString(level21)).toBe(
      [Color.DarkBlue, ' ', Color.Orange, ' ', Color.Red, ' ', Color.DarkBlue, ' ', Color.Pink, ' ', Color.Empty, ' ', Color.Empty, '\n'].join('') +
        [Color.Red, ' ', Color.Red, ' ', Color.LightGreen, ' ', Color.Pink, ' ', Color.LightGreen, ' ', Color.Empty, ' ', Color.Empty, '\n'].join('') +
        [Color.Red, ' ', Color.LightGreen, ' ', Color.DarkBlue, ' ', Color.Orange, ' ', Color.Pink, ' ', Color.Empty, ' ', Color.Empty, '\n'].join('') +
        [Color.Orange, ' ', Color.Pink, ' ', Color.DarkBlue, ' ', Color.LightGreen, ' ', Color.Orange, ' ', Color.Empty, ' ', Color.Empty, '\n'].join(''),
    );
  });
});

describe('canPour', () => {
  // prettier-ignore
  const puzzle: Color[][] = [
    [Color.DarkBlue],
    [],
    [Color.DarkBlue],
    [Color.DarkBlue, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue],
    [Color.Orange]
  ];

  test("canPour with out of bounds 'from'", () => {
    expect(canPour(puzzle, 5, 1)).toBe(false);
  });
  test("canPour with out of bounds 'to'", () => {
    expect(canPour(puzzle, 1, 5)).toBe(false);
  });
  test('canPour from empty self', () => {
    let emptyPuzzle: Color[][] = [[], []];
    expect(canPour(emptyPuzzle, 0, 0)).toBe(false);
  });
  test('canPour from empty to empty', () => {
    let emptyPuzzle: Color[][] = [[], []];
    expect(canPour(emptyPuzzle, 0, 1)).toBe(false);
  });
  test('canPour from dark blue to empty', () => {
    expect(canPour(puzzle, 0, 1)).toBe(true);
  });
  test('canPour from dark blue to dark blue', () => {
    expect(canPour(puzzle, 0, 2)).toBe(true);
  });
  test('canPour from dark blue to full dark blue', () => {
    expect(canPour(puzzle, 0, 3)).toBe(false);
  });
  test('canPour from dark blue to orange', () => {
    expect(canPour(puzzle, 0, 4)).toBe(false);
  });
});

describe('getNumberOfBlocksCanPour', () => {
  test('getNumberOfBlocksCanPour on empty column', () => {
    expect(getNumberOfBlocksCanPour([])).toBe(0);
  });
  test('getNumberOfBlocksCanPour on column with 1 block', () => {
    expect(getNumberOfBlocksCanPour([Color.DarkBlue])).toBe(1);
  });
  test('getNumberOfBlocksCanPour on column with 2 blocks', () => {
    expect(getNumberOfBlocksCanPour([Color.DarkBlue, Color.DarkBlue])).toBe(2);
  });
  test('getNumberOfBlocksCanPour on column with 3 blocks', () => {
    expect(getNumberOfBlocksCanPour([Color.DarkBlue, Color.DarkBlue, Color.DarkBlue])).toBe(3);
  });
  test('getNumberOfBlocksCanPour on column with 4 blocks', () => {
    expect(getNumberOfBlocksCanPour([Color.DarkBlue, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue])).toBe(4);
  });
  test('getNumberOfBlocksCanPour on column with 2 blocks on top of a different color', () => {
    expect(getNumberOfBlocksCanPour([Color.Orange, Color.DarkBlue, Color.DarkBlue])).toBe(2);
  });
  test('getNumberOfBlocksCanPour on column with 3 blocks on top of a different color', () => {
    expect(getNumberOfBlocksCanPour([Color.Orange, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue])).toBe(3);
  });
});

describe('pour', () => {
  test('pour throws when !canPour', () => {
    expect(() => {
      pour([[Color.DarkBlue], [Color.Orange]], 0, 1);
    }).toThrow();
  });
  test('pour one block to empty column', () => {
    expect(pour([[Color.DarkBlue], []], 0, 1)).toEqual([[], [Color.DarkBlue]]);
  });
  test('pour one block to column with one block', () => {
    expect(pour([[Color.DarkBlue], [Color.DarkBlue]], 0, 1)).toEqual([[], [Color.DarkBlue, Color.DarkBlue]]);
  });
  test('pour two blocks to empty column', () => {
    expect(pour([[Color.DarkBlue, Color.DarkBlue], []], 0, 1)).toEqual([[], [Color.DarkBlue, Color.DarkBlue]]);
  });
  test('pour two blocks to column with space for two blocks', () => {
    expect(pour([[Color.DarkBlue, Color.DarkBlue], [Color.DarkBlue]], 0, 1)).toEqual([[], [Color.DarkBlue, Color.DarkBlue, Color.DarkBlue]]);
  });
  test('pour two blocks to column with space for only one block', () => {
    expect(
      pour(
        [
          [Color.DarkBlue, Color.DarkBlue],
          [Color.DarkBlue, Color.DarkBlue, Color.DarkBlue],
        ],
        0,
        1,
      ),
    ).toEqual([[Color.DarkBlue], [Color.DarkBlue, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue]]);
  });
});

describe('getNextStates', () => {
  test('getNextStates simple', () => {
    let state: State = {
      puzzle: [[Color.DarkBlue], [Color.DarkBlue], []],
    };
    expect(getNextStates(state)).toEqual([
      {
        puzzle: [[], [Color.DarkBlue, Color.DarkBlue], []],
        previousState: state,
      },
      {
        puzzle: [[], [Color.DarkBlue], [Color.DarkBlue]],
        previousState: state,
      },
      {
        puzzle: [[Color.DarkBlue, Color.DarkBlue], [], []],
        previousState: state,
      },
      {
        puzzle: [[Color.DarkBlue], [], [Color.DarkBlue]],
        previousState: state,
      },
    ]);
  });
  test('getNextStates on level2', () => {
    let state: State = {
      puzzle: level2,
    };

    expect(getNextStates(state)).toEqual([
      {
        puzzle: [[Color.DarkBlue, Color.Orange, Color.DarkBlue], [Color.Orange, Color.DarkBlue, Color.Orange, Color.DarkBlue], [Color.Orange]],
        previousState: state,
      },
      {
        puzzle: [[Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.Orange], [Color.Orange, Color.DarkBlue, Color.Orange], [Color.DarkBlue]],
        previousState: state,
      },
    ]);
  });
  test('getNextStates run twice to validate previousState', () => {
    let state1: State = {
      puzzle: [[Color.DarkBlue], [Color.DarkBlue], []],
    };

    let state2: State = getNextStates(state1)[0];
    let state3: State = getNextStates(state2)[0];
    expect(state3.previousState).toEqual(state2);
    expect(state3.previousState?.previousState).toEqual(state1);
  });
});

describe('getSortedColumns', () => {
  test('column order doesnt change if equal columns are sorted', () => {
    expect(
      getSortColumns([
        [Color.DarkBlue, Color.DarkBlue],
        [Color.DarkBlue, Color.DarkBlue],
      ]),
    ).toEqual([
      [Color.DarkBlue, Color.DarkBlue],
      [Color.DarkBlue, Color.DarkBlue],
    ]);
  });
  test('column order reverse when sorted', () => {
    expect(
      getSortColumns([
        [Color.DarkBlue, Color.Orange],
        [Color.DarkBlue, Color.DarkBlue],
        [Color.DarkBlue, Color.ArmyGreen],
      ]),
    ).toEqual([
      [Color.DarkBlue, Color.ArmyGreen],
      [Color.DarkBlue, Color.DarkBlue],
      [Color.DarkBlue, Color.Orange],
    ]);
  });
  test('empty columns sort correctly', () => {
    expect(getSortColumns([[], [Color.DarkBlue], [Color.Orange]])).toEqual([[Color.DarkBlue], [Color.Orange], []]);
  });
});

describe('solvePuzzle', () => {
  test('solve simple puzzle', () => {
    // prettier-ignore
    expect(solvePuzzle(simplePuzzle)).toMatchObject([
      { puzzle: [[Color.DarkBlue, Color.DarkBlue, Color.DarkBlue], [Color.Orange, Color.Orange, Color.Orange], [Color.Orange, Color.DarkBlue]] },
      { puzzle: [[Color.DarkBlue, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue], [Color.Orange, Color.Orange, Color.Orange], [Color.Orange]] },
      { puzzle: [[Color.DarkBlue, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue], [], [Color.Orange, Color.Orange, Color.Orange, Color.Orange]] },
    ]);
  });
  test('solve level2 puzzle', () => {
    // prettier-ignore
    expect(solvePuzzle(level2)).toMatchObject([
      { puzzle: [[Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.Orange], [Color.Orange, Color.DarkBlue, Color.Orange, Color.DarkBlue], []] },
      { puzzle: [[Color.DarkBlue, Color.Orange, Color.DarkBlue], [Color.Orange, Color.DarkBlue, Color.Orange, Color.DarkBlue], [Color.Orange]] },
      { puzzle: [[Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.DarkBlue], [Color.Orange, Color.DarkBlue, Color.Orange], [Color.Orange]] },
      { puzzle: [[Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.DarkBlue], [Color.Orange, Color.DarkBlue], [Color.Orange, Color.Orange]] },
      { puzzle: [[Color.DarkBlue, Color.Orange], [Color.Orange, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue], [Color.Orange, Color.Orange]] },
      { puzzle: [[Color.DarkBlue], [Color.Orange, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue], [Color.Orange, Color.Orange, Color.Orange]] },
      { puzzle: [[Color.DarkBlue, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue], [Color.Orange], [Color.Orange, Color.Orange, Color.Orange]] },
      { puzzle: [[Color.DarkBlue, Color.DarkBlue, Color.DarkBlue, Color.DarkBlue], [], [Color.Orange, Color.Orange, Color.Orange, Color.Orange]] },
    ]);
  });
});
