// @ts-ignore
import Hashes from 'jshashes';

const SHA256 = new Hashes.SHA256();

export enum Color {
  ArmyGreen = 'AG',
  Brown = 'BR',
  DarkBlue = 'DB',
  DarkGreen = 'DG',
  Empty = '__',
  Grey = 'GY',
  LightBlue = 'LB',
  LightGreen = 'LG',
  Orange = 'OR',
  Pink = 'PK',
  Purple = 'PR',
  Red = 'RD',
  Yellow = 'YL',
}

export interface State {
  puzzle: Color[][];
  previousState?: State;
}

// prettier-ignore
const simplePuzzle: Color[][] = [
  [Color.DarkBlue, Color.DarkBlue, Color.DarkBlue],
  [Color.Orange, Color.Orange, Color.Orange],
  [Color.Orange, Color.DarkBlue],
];

// prettier-ignore
const level2: Color[][] = [
  [Color.DarkBlue, Color.Orange, Color.DarkBlue, Color.Orange],
  [Color.Orange, Color.DarkBlue, Color.Orange, Color.DarkBlue],
  []
];

// prettier-ignore
const level3: Color[][] = [
  [Color.DarkBlue, Color.Orange, Color.Red, Color.DarkBlue],
  [Color.Orange, Color.Orange, Color.Red, Color.DarkBlue],
  [Color.Red, Color.DarkBlue, Color.Orange, Color.Red],
  [],
  []
];

const level5: Color[][] = [
  [Color.LightGreen, Color.Orange, Color.DarkBlue, Color.Pink],
  [Color.Orange, Color.LightGreen, Color.DarkBlue, Color.Pink],
  [Color.Pink, Color.Red, Color.Orange, Color.Red],
  [Color.Orange, Color.Pink, Color.Red, Color.DarkBlue],
  [Color.LightGreen, Color.LightGreen, Color.Red, Color.DarkBlue],
  [],
  [],
];

const level21: Color[][] = [
  [Color.Orange, Color.Red, Color.Red, Color.DarkBlue],
  [Color.Pink, Color.LightGreen, Color.Red, Color.Orange],
  [Color.DarkBlue, Color.DarkBlue, Color.LightGreen, Color.Red],
  [Color.LightGreen, Color.Orange, Color.Pink, Color.DarkBlue],
  [Color.Orange, Color.Pink, Color.LightGreen, Color.Pink],
  [],
  [],
];

const level1000: Color[][] = [
  [Color.Grey, Color.Pink, Color.LightBlue, Color.ArmyGreen],
  [Color.Orange, Color.ArmyGreen, Color.LightBlue, Color.LightBlue],
  [Color.Purple, Color.Grey, Color.Grey, Color.DarkBlue],
  [Color.Red, Color.Pink, Color.Red, Color.Orange],
  [Color.LightGreen, Color.ArmyGreen, Color.Pink, Color.Orange],
  [Color.Red, Color.DarkBlue, Color.DarkBlue, Color.LightGreen],
  [Color.Purple, Color.LightGreen, Color.Purple, Color.Red],
  [Color.DarkBlue, Color.LightGreen, Color.Pink, Color.Purple],
  [Color.LightBlue, Color.ArmyGreen, Color.Orange, Color.Grey],
  [],
  [],
];

const level4861: Color[][] = [
  [Color.LightGreen, Color.DarkGreen, Color.ArmyGreen, Color.ArmyGreen],
  [Color.Grey, Color.Red, Color.LightGreen, Color.Orange],
  [Color.Pink, Color.Pink, Color.ArmyGreen, Color.Grey],
  [Color.LightBlue, Color.Pink, Color.Brown, Color.Brown],
  [Color.DarkGreen, Color.Orange, Color.Purple, Color.Red],
  [Color.Orange, Color.Yellow, Color.Red, Color.LightBlue],
  [Color.Brown, Color.DarkBlue, Color.Yellow, Color.LightBlue],
  [Color.Yellow, Color.DarkBlue, Color.DarkBlue, Color.Orange],
  [Color.LightBlue, Color.Purple, Color.Purple, Color.DarkBlue],
  [Color.Red, Color.Pink, Color.DarkGreen, Color.Grey],
  [Color.LightGreen, Color.Purple, Color.LightGreen, Color.Grey],
  [Color.ArmyGreen, Color.Brown, Color.Yellow, Color.DarkGreen],
  [],
  [],
];

export function isColumnSolved(column: Color[]): boolean {
  if (column.length == 0) {
    return true;
  } else if (column.length == 4 && column.every((x) => x === column[0])) {
    return true;
  }
  return false;
}

export function isPuzzleSolved(puzzle: Color[][]): boolean {
  return puzzle.every((x) => isColumnSolved(x));
}

export function toPrettyString(puzzle: Color[][]): string {
  let result = '';
  for (let row = 3; row >= 0; row--) {
    let line: string = '';
    for (let column = 0; column < puzzle.length; column++) {
      if (puzzle[column][row]) {
        line += puzzle[column][row];
      } else {
        line += Color.Empty;
      }
      line += ' ';
    }
    result += line.trim() + '\n';
  }
  return result;
}

export function toStringPuzzle(puzzle: Color[][]): string {
  return JSON.stringify(puzzle);
}

export function toStringState(state: State): string {
  return JSON.stringify(state.puzzle);
}

export function toStringStates(states: State[]): string {
  return states.map((state) => toStringState(state)).join('\n');
}

export function getHash(state: State): string {
  return SHA256.hex(toStringPuzzle(getSortColumns(state.puzzle)));
}

export function canPour(puzzle: Color[][], from: number, to: number): boolean {
  if (from >= puzzle.length || to >= puzzle.length) {
    return false;
  }

  if (isColumnSolved(puzzle[from])) {
    return false;
  }

  let fromColumn: Color[] = puzzle[from];
  let toColumn: Color[] = puzzle[to];
  if (fromColumn.length > 0 && toColumn.length < 4) {
    return toColumn.length == 0 || fromColumn[fromColumn.length - 1] === toColumn[toColumn.length - 1];
  }

  return false;
}

export function getNumberOfBlocksCanPour(column: Color[]): number {
  if (column.length == 0) {
    return 0;
  }

  let lastIndex = column.length - 1;
  if (column.length > 3 && column[lastIndex] === column[lastIndex - 1] && column[lastIndex] === column[lastIndex - 2] && column[lastIndex] === column[lastIndex - 3]) {
    return 4;
  } else if (column.length > 2 && column[lastIndex] === column[lastIndex - 1] && column[lastIndex] === column[lastIndex - 2]) {
    return 3;
  } else if (column.length > 1 && column[lastIndex] === column[lastIndex - 1]) {
    return 2;
  } else {
    return 1;
  }
}

export function pour(puzzle: Color[][], from: number, to: number): Color[][] {
  if (!canPour(puzzle, from, to)) {
    throw new Error(`Cannot pour from ${from} to ${to} on puzzle:\n${toStringPuzzle(puzzle)}`);
  }

  let fromColumn: Color[] = puzzle[from];
  let toColumn: Color[] = puzzle[to];

  let numberOfBlocksCanPour = getNumberOfBlocksCanPour(fromColumn);
  let numberOfSpacesForPour = 4 - toColumn.length;

  while (numberOfBlocksCanPour > 0 && numberOfSpacesForPour > 0) {
    toColumn.push(fromColumn.pop()!);
    numberOfBlocksCanPour--;
    numberOfSpacesForPour--;
  }

  return puzzle;
}

export function getNextStates(state: State): State[] {
  let result: State[] = [];
  for (let from = 0; from < state.puzzle.length; from++) {
    for (let to = 0; to < state.puzzle.length; to++) {
      if (from != to && canPour(state.puzzle, from, to)) {
        let nextState: State = {
          puzzle: pour(JSON.parse(JSON.stringify(state.puzzle)), from, to),
          previousState: JSON.parse(JSON.stringify(state)),
        };
        result.push(nextState);
      }
    }
  }
  return result;
}

export function getSortColumns(puzzle: Color[][]): Color[][] {
  let strings: string[] = [];
  for (let column of puzzle) {
    strings.push(JSON.stringify(column));
  }

  strings.sort();

  let sorted: Color[][] = [];
  for (let column of strings) {
    sorted.push(JSON.parse(column));
  }

  return sorted;
}

export function solvePuzzle(puzzle: Color[][]): State[] {
  let initialState: State = { puzzle };
  let stateQueue: State[] = [initialState];
  let visitedStates = new Set<string>();

  let state = stateQueue.shift()!;
  visitedStates.add(getHash(state));
  while (!isPuzzleSolved(state.puzzle)) {
    let nextStates = getNextStates(state);

    for (let nextState of nextStates) {
      let hash = getHash(nextState);
      if (!visitedStates.has(hash)) {
        visitedStates.add(hash);
        stateQueue.push(nextState);
      }
    }

    state = stateQueue.shift()!;
    if (!state) {
      console.error('NO SOLUTION FOUND');
      return [];
    }
  }

  let steps: State[] = [state];
  while (state.previousState) {
    steps.unshift(state.previousState);
    state = state.previousState;
  }

  return steps;
}

let puzzle: Color[][] = level4861;
console.log('puzzle: ', toStringPuzzle(puzzle));
let steps = solvePuzzle(puzzle);
for (let i = 0; i < steps.length; i++) {
  let step = steps[i];
  console.log('step ' + i + ':\n' + toPrettyString(step.puzzle));
}

module.exports = {
  Color,
  simplePuzzle,
  level2,
  level5,
  level21,
  isColumnSolved,
  isPuzzleSolved,
  toPrettyString,
  toStringPuzzle,
  toStringState,
  toStringStates,
  canPour,
  getNumberOfBlocksCanPour,
  pour,
  getNextStates,
  getSortColumns,
  solvePuzzle,
};
