.text {
  z-index: var(--layer-text);
  display: block;
  padding: 0.5rem 1rem;
  border: 0.2rem solid var(--black);
  margin: 0;
  background: #ffe4b5;
  box-shadow: 0.3rem 0.3rem 0 var(--black);
  text-align: center;
  transform-origin: center;
  transition: all var(--animation-duration) ease;

  @starting-style {
    opacity: 0;
    rotate: 0deg;
    scale: 0.75;
  }
}

.white {
  background: var(--white);
}
