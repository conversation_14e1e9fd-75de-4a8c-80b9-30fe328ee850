.logo {
  position: relative;
  z-index: var(--layer-ui);
  display: block;
  width: 100%;
  margin: 0 auto;
  font-family: Bang<PERSON>, sans-serif;
  font-weight: 400;
  rotate: -2.5deg;
  text-align: center;
  transition: all 0.25s ease;
  white-space: nowrap;
}

.link {
  display: block;
  color: inherit;
  text-decoration: none;
}

.the {
  display: block;
  margin-bottom: -0.25rem;
  font-size: 1.25rem;
  line-height: 1;
}

.gerrymanderer {
  display: block;
  font-size: 3rem;
  line-height: 1;
}


/* yellow text */
.yellow {
  position: relative;
  z-index: 2;
  display: block;
  background-clip: text;
  background-image: linear-gradient(
    to bottom,
    #fffde6 0%,
    #fef88f 25%,
    #f9de58 50%,
    #f4ba4b 75%,
    #eb9d3fff 100%
  );
  color: transparent;
}

/* shadow text */
.shadow {
  position: absolute;
  z-index: 1;
  display: block;
  inset: 0;
}

.shadow .gerrymanderer {
  text-shadow:
    0.35rem 0 var(--black),
    -0.25rem 0 var(--black),
    0 0.35rem var(--black),
    0 -0.25rem var(--black);
}

.shadow .the {
  text-shadow:
    0 0.15rem 0 var(--black),
    0 -0.15rem 0 var(--black),
    0.15rem 0.15rem 0 var(--black),
    0.15rem 0 0 var(--black),
    0.15rem -0.15rem 0 var(--black),
    -0.15rem 0.15rem 0 var(--black),
    -0.15rem 0 0 var(--black),
    -0.15rem -0.15rem 0 var(--black);
}

.home {
  rotate: -10deg;
  scale: 1.25;
  transition: all 0.5s cubic-bezier(0.68, -0.6, 0.32, 1.6);
  translate: 0 3rem;

  @starting-style {
    opacity: 0;
    rotate: 0deg;
    scale: 0;
  }
}
