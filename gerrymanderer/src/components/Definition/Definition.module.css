.dictionary {
  z-index: var(--layer-text);
  padding: 1.25rem 1rem 1.5rem;
  border: 0.2rem solid var(--black);
  background-color: var(--white);
  box-shadow: 0.3rem 0.3rem 0 var(--black);
  font-family: Merriweather, serif;
  line-height: 1.25;
  text-align: left;
  transform-origin: center;
  transition-duration: var(--animation-duration);
  transition-property: opacity, scale, rotate;
  transition-timing-function: ease;

  @starting-style {
    opacity: 0;
    rotate: 0deg;
    scale: 0.95;
  }
}

.term {
  font-size: 1.25rem;
  font-weight: 900;
}

.pronunciation {
  margin-bottom: 1rem;
}

.definition {
  text-wrap: pretty;
}
