.button {
  z-index: 1;
  flex-shrink: 0;
  padding: 0.5rem 0.75rem;
  border: 0.25rem solid var(--black);
  border-radius: 0;
  background-color: var(--purple);
  box-shadow:
    inset 0.1rem 0.1rem 0 rgb(255 255 255 / 30%),
    inset -0.1rem -0.1rem 0 rgb(0 0 0 / 30%),
    0.1rem 0.1rem 0 var(--black);
  color: white;
  cursor: pointer;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1;
  outline: none;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  transform-origin: center;
  transition-duration: var(--animation-duration);
  transition-property: opacity, scale, rotate;
  transition-timing-function: ease;

  @starting-style {
    opacity: 0;
    rotate: 0deg;
    scale: 0.95;
  }
}

@media (hover: hover) {
  .button:hover {
    rotate: 1deg;
    scale: 1.05;
  }
}

.button:has(svg) {
  padding: 0.125rem;
}

.button:active {
  rotate: -1deg;
  scale: 0.95;
}

.button svg {
  display: block;
  width: 2rem;
  height: 2rem;
  fill: currentcolor;
}

.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
}

.disabled:hover {
  rotate: 0deg;
  scale: 1;
}

.disabled:active {
  rotate: 0deg;
  scale: 1;
}
