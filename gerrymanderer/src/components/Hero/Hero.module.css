.hero {
  max-width: 16rem;
  height: auto;
  margin: 1rem 0 -2rem;
  animation: floating 2s ease-in-out infinite;
  aspect-ratio: 1;
  filter: drop-shadow(0 0.1em 0.1em rgb(0 0 0 / 50%));
  transform-origin: center bottom;
  transition-duration: var(--animation-duration);
  transition-property: opacity, scale;
  transition-timing-function: ease;

  @starting-style {
    opacity: 0;
    scale: 0.5;
  }
}

@keyframes floating {
  0% {
    translate: 0 -0.5rem;
  }

  50% {
    translate: 0;
  }

  100% {
    translate: 0 -0.5rem;
  }
}
