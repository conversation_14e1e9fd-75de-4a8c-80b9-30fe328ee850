.bubble {
  position: relative;
  padding: 0.5rem 0.75rem;
  border: 0.2rem solid var(--black);
  border-radius: 0.5rem;
  background: var(--white);
  font-size: 0.75rem;
  line-height: 1;
  text-transform: uppercase;
  transform-origin: bottom center;
  transition-duration: var(--animation-duration);
  transition-property: opacity, scale, translate;
  white-space: nowrap;

  @starting-style {
    opacity: 0;
    scale: 0.5;
    translate: 0 1rem;
  }
}

.left {
  @starting-style {
    translate: -2rem 1rem;
  }
}

.right {
  @starting-style {
    translate: 2rem 1rem;
  }
}

.arrows {
  position: absolute;
  display: flex;
  width: 100%;
  justify-content: space-around;
  padding: 0 1rem;
  inset: calc(100% - 0.25rem) 0 0;
}

.arrow {
  display: block;
  width: 0.75rem;
  height: 0.75rem;
  border-bottom: 0.2rem solid var(--black);
  border-left: 0.2rem solid var(--black);
  background: white;
  rotate: -45deg;
}

.left .arrows {
  justify-content: start;
}

.right .arrows {
  justify-content: end;
}

.all .arrows {
  justify-content: space-between;
}
