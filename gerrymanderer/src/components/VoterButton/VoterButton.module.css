.voter {
  position: relative;
  border: none;
  aspect-ratio: 1;
  background: none;
  cursor: pointer;
  font-size: calc(10rem / var(--size));
}

.red {
  background: linear-gradient(135deg, #dc143c, #b22222 50%, #8b0000);
}

.blue {
  background: linear-gradient(135deg, #06c, #0052a3 50%, #003d7a);
}

/* empty cell */
.empty {
  pointer-events: none;
}

/* when a nearby voter is selected */
.selectable {
  position: absolute;
  border: 0.075em dashed rgb(255 255 255 / 50%);
  border-radius: 100%;
  animation: rotate 10s linear infinite;
  inset: 0.35em;
  pointer-events: none;
}

/* selected state = staged for selection */
.red.selected {
  background: var(--red);
}

.blue.selected {
  background: var(--blue);
}

/* completed state = no longer in game play */
.completed {
  pointer-events: none;
}

.district-red {
  background: var(--red);
}

.district-blue {
  background: var(--blue);
}

.district-purple {
  background: var(--purple);
}

/* completed with district mismatch */
.original-color {
  position: absolute;
  border-radius: 100%;
  inset: 0.35em;
  pointer-events: none;
}

.red .original-color {
  background: var(--red);
}

.blue .original-color {
  background: var(--blue);
}

.purple .original-color {
  background: var(--purple);
}

/* face emoji */
.face {
  position: relative;
  filter: drop-shadow(0 0.1em 0.1em rgb(0 0 0 / 50%));
  line-height: 2;
  pointer-events: none;
}

.elated .face {
  animation: elated 1s infinite;
}

.sad .face {
  animation: sad 0.5s linear infinite;
}

/* district borders made up of 4 layered gradients */
.borders {
  position: absolute;
  z-index: 1;
  display: block;
  inset: 0;
  pointer-events: none;
}

.borders .top,
.borders .right,
.borders .bottom,
.borders .left {
  position: absolute;
  display: block;
  border-style: solid;
  border-image-outset: calc(var(--grid-cell-border-image-size) / 2);
  border-image-repeat: repeat;
  border-image-slice: var(--grid-cell-border-image-slice);
  border-image-width: var(--grid-cell-border-image-size);
  inset: 0;
  pointer-events: none;
}

.borders .top {
  border-image-source: var(--grid-cell-border-image-top);
}

.borders .right {
  border-image-source: var(--grid-cell-border-image-right);
}

.borders .bottom {
  border-image-source: var(--grid-cell-border-image-bottom);
}

.borders .left {
  border-image-source: var(--grid-cell-border-image-left);
}

/* animations */

@media (hover: hover) {
  .face {
    transition: scale 0.1s ease-in;
  }

  .voter:hover .face {
    scale: 1.2;
    transition: scale 0.1s ease-out;
  }

  .voter:active .face {
    scale: 1;
    transition: scale 0.1s ease-out;
  }
}

@media (hover: none) {
  .face {
    transition: scale 0.05s ease-in;
  }

  .voter:active .face {
    scale: 0.9;
    transition: scale 0.15s ease-out;
  }
}

@keyframes elated {
  0% {
    rotate: -15deg;
  }

  50% {
    rotate: 15deg;
  }

  100% {
    rotate: -15deg;
  }
}

@keyframes sad {
  0% {
    translate: 0 -0.1rem;
  }

  50% {
    translate: 0;
  }

  100% {
    translate: 0 -0.1rem;
  }
}

@keyframes rotate {
  0% {
    rotate: 0;
  }

  100% {
    rotate: 360deg;
  }
}
