html {
  color: var(--black);
  font-family: 'Permanent Marker', sans-serif;
  font-size: clamp(16px, min(2.25dvh, 2.25dvw), 24px);
  line-height: 1.25;
  transition: font-size var(--animation-duration) ease;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  text-wrap: balance;
}

h2 {
  font-size: 1.125rem;
}

.text-blue {
  color: var(--blue);
  text-transform: uppercase;
}

.text-red {
  color: var(--red);
  text-transform: uppercase;
}
