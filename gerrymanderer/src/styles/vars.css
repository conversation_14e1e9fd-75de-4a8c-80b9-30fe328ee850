:root {
  /* colors */
  --red: #dc143c;
  --blue: #06c;
  --purple: #814c9c;
  --white: #f0f0f0;
  --black: #222;

  /* animation */
  --animation-duration: 0.25s;

  /* layering */
  --layer-base: 10;
  --layer-text: 20;
  --layer-ui: 30;

  /* grid */
  --grid-cell-border-color: var(--white);
  --grid-cell-border-image-slice: 5;
  --grid-cell-border-image-size: calc(
    var(--grid-cell-border-image-slice) * 1px
  );
  --grid-cell-border-image-top: linear-gradient(
    to bottom,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
  --grid-cell-border-image-right: linear-gradient(
    to left,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
  --grid-cell-border-image-bottom: linear-gradient(
    to top,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
  --grid-cell-border-image-left: linear-gradient(
    to right,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
}
