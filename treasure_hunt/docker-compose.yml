version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: treasure_hunt_db
    environment:
      POSTGRES_DB: treasure_hunt
      POSTGRES_USER: treasure_hunt
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U treasure_hunt -d treasure_hunt"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data: