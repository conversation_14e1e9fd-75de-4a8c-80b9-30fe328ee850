{"name": "treasure-hunt-app", "version": "1.0.0", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"yarn db:up\" \"yarn workspace backend start:dev\" \"yarn workspace frontend dev\"", "dev:backend": "concurrently \"yarn db:up\" \"yarn workspace backend start:dev\"", "dev:frontend": "yarn workspace frontend dev", "build": "yarn workspace backend build && yarn workspace frontend build", "build:backend": "yarn workspace backend build", "build:frontend": "yarn workspace frontend build", "start": "concurrently \"yarn workspace backend start:prod\" \"yarn workspace frontend start\"", "start:backend": "yarn workspace backend start:prod", "start:frontend": "yarn workspace frontend start", "test": "yarn workspace backend test && yarn workspace frontend test", "test:backend": "yarn workspace backend test", "test:frontend": "yarn workspace frontend test", "test:coverage": "yarn workspace backend test:cov && yarn workspace frontend test:coverage", "prepare": "husky", "check-format": "prettier --check '**/*.ts' '**/*.tsx'", "format": "prettier --write '**/*.ts' '**/*.tsx'", "clean": "rm -rf node_modules yarn.lock backend/dist backend/node_modules frontend/.next frontend/node_modules && yarn cache clean", "lint": "yarn workspace backend lint && yarn workspace frontend lint", "lint:backend": "yarn workspace backend lint", "lint:frontend": "yarn workspace frontend lint", "type-check": "yarn workspace backend type-check && yarn workspace frontend type-check", "type-check:backend": "yarn workspace backend type-check", "type-check:frontend": "yarn workspace frontend type-check", "data:export": "node export_data.js", "data:import": "node import_data.js", "db:up": "docker-compose up -d postgres", "db:down": "docker-compose down", "db:logs": "docker-compose logs -f postgres", "db:reset": "docker-compose down -v && docker-compose up -d postgres"}, "dependencies": {"concurrently": "^8.2.2", "node-fetch": "^3.3.2"}, "lint-staged": {"**/*.ts": "prettier --write --ignore-unknown", "**/*.tsx": "prettier --write --ignore-unknown"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "husky": "^9.1.7", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "lint-staged": "^16.1.4", "prettier": "^3.6.2", "typescript": "^5"}}