{"name": "frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"canvas-confetti": "^1.9.3", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "qrcode": "^1.5.4", "qrcode.react": "^3.1.0", "qr-scanner": "^1.4.2", "swr": "^2.2.5"}, "devDependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/canvas-confetti": "^1.6.4", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.6.2", "typescript": "^5"}}