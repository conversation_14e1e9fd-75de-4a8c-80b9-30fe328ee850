/* Play Page Specific Styles */

.huntTitle {
  color: var(--color-primary);
  font-size: 2rem;
  margin: 0;
  font-family: 'Creepster', cursive;
}

.progressCard {
  background-color: var(--color-surface-accent);
}

.progressContent {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.progressBar {
  width: 100%;
  height: 20px;
  background: rgba(218, 165, 32, 0.2);
  border-radius: 10px;
  overflow: hidden;
}

.progressBarFill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-secondary) 0%, var(--color-secondary-hover) 100%);
  transition: width 0.3s ease;
  border-radius: 10px;
}

.progressText {
  font-size: 0.9rem;
  color: var(--color-primary);
  font-weight: 600;
  text-align: center;
}

/* Clue Section */
.clueSection {
  margin-bottom: 2rem;
}

.clueHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.clueHeader h2 {
  color: var(--color-primary);
  font-size: 1.5rem;
  margin: 0;
}

.clueCard {
  background-color: var(--color-surface-accent);
}

.clueContent {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.clueText {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 2px solid var(--color-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  color: #5a4037;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-style: italic;
  white-space: pre-line;
}

.clueActions {
  display: flex;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }

  .header {
    margin-bottom: 1rem;
  }

  .huntTitle {
    font-size: 1.5rem;
  }

  .treasureHeader, .progressHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .treasureHeader h2, .progressHeader h2 {
    text-align: center;
    font-size: 1.25rem;
  }

  .clueText {
    padding: 1rem;
    font-size: 1rem;
    min-height: 60px;
  }
}

@media (max-width: 480px) {
  .huntTitle {
    font-size: 1.25rem;
  }

  .treasureHeader h2, .progressHeader h2 {
    font-size: 1rem;
  }

  .progressText {
    font-size: 0.8rem;
  }

  .clueText {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
}

/* Celebration Overlay */
.celebrationOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in;
}

.celebrationContent {
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: bounceIn 0.6s ease-out;
  position: relative;
  z-index: 1001;
}

.celebrationEmoji {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

.celebrationText h2 {
  color: var(--color-primary);
  font-size: 2rem;
  margin: 0 0 1rem 0;
  font-family: 'Creepster', cursive;
}

.celebrationText p {
  font-size: 1.1rem;
  color: #5a4037;
  margin: 0.5rem 0;
}

.celebrationActions {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}


/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3) translateY(-100px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(0);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}


/* Responsive Celebration */
@media (max-width: 768px) {
  .celebrationOverlay {
    padding-top: 5vh;
  }

  .celebrationContent {
    padding: 1.25rem;
    margin: 1rem;
  }

  .celebrationEmoji {
    font-size: 3rem;
  }

  .celebrationText h2 {
    font-size: 1.5rem;
  }

  .celebrationText p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .celebrationOverlay {
    padding-top: 3vh;
  }

  .celebrationContent {
    padding: 1rem;
  }

  .celebrationEmoji {
    font-size: 2.5rem;
  }

  .celebrationText h2 {
    font-size: 1.25rem;
  }
}