/* Utility Classes for Common Patterns */

/* Container Pattern - Used across pages */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Header Pattern - Used for page headers with actions */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

/* Section Header Pattern - Used for content sections */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  color: var(--color-primary);
  font-size: 1.5rem;
  margin: 0;
}

/* Loading and Error States */
.loading-state, .error-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--color-primary);
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.error-state h2 {
  color: var(--color-danger);
  margin-bottom: 1rem;
}

/* Section Pattern - Common content sections */
.content-section {
  margin-bottom: 2rem;
}

/* Title Section Pattern */
.title-section {
  margin-bottom: 2rem;
  text-align: center;
}

/* Empty State Pattern */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
  font-style: italic;
}

/* Responsive Adjustments for Utilities */
@media (max-width: 768px) {
  .page-container {
    padding: 0.5rem;
  }

  .page-header {
    margin-bottom: 1rem;
  }

  .section-header {
    gap: 1rem;
  }

  .section-header h2 {
    font-size: 1.25rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .section-header h2 {
    font-size: 1rem;
  }
}