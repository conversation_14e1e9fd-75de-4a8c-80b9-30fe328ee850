.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  @starting-style {
    opacity: 0;
  }
}

.modal {
  background: var(--color-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  
  @starting-style {
    opacity: 0;
    scale: 0.9;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-secondary-light) 100%);
}

.header h2 {
  color: var(--color-primary-dark);
  margin: 0;
  font-size: var(--font-size-lg);
  font-family: var(--font-heading);
}

.content {
  padding: var(--spacing-lg);
}

.error {
  text-align: center;
  color: var(--color-danger);
}

.error p {
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-base);
}

.cameraSelector {
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.cameraSelector label {
  font-weight: 600;
  color: var(--color-text);
  min-width: 60px;
}

.select {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

.select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.instructions {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  
  p {
    margin-bottom: var(--spacing-xs);
  }
  
  p:first-child {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
  }
}

.videoContainer {
  position: relative;
  background: var(--color-black);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  aspect-ratio: 4/3;
  max-height: 300px;
}

.video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scanRegion {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid var(--color-accent);
  border-radius: var(--border-radius);
  box-shadow: 
    0 0 0 9999px rgba(0, 0, 0, 0.3),
    inset 0 0 0 2px rgba(255, 99, 71, 0.5);
  pointer-events: none;
}

.scanRegion::before,
.scanRegion::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid var(--color-accent);
  border-radius: 2px;
}

.scanRegion::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.scanRegion::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

.actions {
  display: flex;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    width: 95vw;
    margin: var(--spacing-md);
  }
  
  .header {
    padding: var(--spacing-md);
  }
  
  .content {
    padding: var(--spacing-md);
  }
  
  .videoContainer {
    max-height: 250px;
  }
  
  .scanRegion {
    width: 150px;
    height: 150px;
  }
}