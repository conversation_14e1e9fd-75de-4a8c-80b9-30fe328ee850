.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-primary);
  font-weight: 500;
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing);
  text-decoration: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  @starting-style {
    opacity: 0;
    scale: 0.95;
  }
}

@media (hover: hover) {
  .button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    scale: 1.02;
  }
}

.button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Variants */
.primary {
  background-color: var(--color-primary);
  color: var(--color-background);
  border-color: var(--color-primary);
}

.primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.secondary {
  background-color: var(--color-secondary);
  color: var(--color-text);
  border-color: var(--color-secondary);
}

.secondary:hover:not(:disabled) {
  background-color: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.danger {
  background-color: var(--color-danger);
  color: var(--color-background);
  border-color: var(--color-danger);
}

.danger:hover:not(:disabled) {
  background-color: var(--color-danger-hover);
  border-color: var(--color-danger-hover);
}

/* Sizes */
.small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.medium {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
}

.large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-lg);
}