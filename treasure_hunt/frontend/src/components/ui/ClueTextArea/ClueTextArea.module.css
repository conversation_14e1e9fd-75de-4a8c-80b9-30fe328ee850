.container {
  width: 100%;
}

.textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--color-secondary);
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-family: inherit;
  background: white;
  resize: vertical;
  transition: border-color 0.2s ease;
  color: var(--color-text);
  line-height: 1.4;
  min-height: 80px;
}

.textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(139, 69, 19, 0.1);
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  height: 1.5rem;
  gap: 1rem;
}

.charCount {
  font-size: 0.85rem;
  color: #666;
  margin-left: auto;
}

.invalid {
  border-color: #dc3545 !important;
  background-color: #fff5f5 !important;
}

.invalid:focus {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1) !important;
}

.blinking {
  animation: errorBlink 0.8s ease-in-out 1;
}

@keyframes errorBlink {
  0% { border-color: #dc3545; }
  25% { border-color: #ff6b6b; }
  50% { border-color: #dc3545; }
  75% { border-color: #ff6b6b; }
  100% { border-color: #dc3545; }
}