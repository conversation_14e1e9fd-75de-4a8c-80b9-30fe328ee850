"use client";

import { useEffect, useState, useRef } from "react";
import QRCode from "qrcode";
import QrScanner from "qr-scanner";

interface QrImageProps {
  qrCodeData: string;
  alt: string;
  className?: string;
  clickable?: boolean;
  onUpload?: (newQrData: string) => void;
  allowUpload?: boolean;
}

export default function QrImage({
  qrCodeData,
  alt,
  className,
  clickable = true,
  onUpload,
  allowUpload = false,
}: QrImageProps) {
  const [imageSrc, setImageSrc] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const generateQRCode = async () => {
      try {
        setLoading(true);
        setError(false);

        const qrCodeUrl = await QRCode.toDataURL(qrCodeData, {
          width: 200,
          margin: 2,
          color: {
            dark: "#8B4513", // Pirate brown
            light: "#FFF8DC", // Cornsilk background
          },
        });

        setImageSrc(qrCodeUrl);
      } catch (err) {
        setError(true);
        console.error("Error generating QR code:", err);
      } finally {
        setLoading(false);
      }
    };

    if (qrCodeData) {
      generateQRCode();
    } else {
      setLoading(false);
      setError(false);
      setImageSrc("");
    }
  }, [qrCodeData]);

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset file input
    event.target.value = "";

    // Validate file type
    if (!file.type.startsWith("image/")) {
      setUploadError("Please select an image file.");
      return;
    }

    try {
      setUploading(true);
      setUploadError(null);

      // Scan QR code from uploaded image
      const qrData = await QrScanner.scanImage(file);

      // Validate that the QR code starts with "treasure-"
      if (!qrData.startsWith("treasure-")) {
        setUploadError(
          'Invalid QR code. Must be generated by this app (starts with "treasure-").',
        );
        return;
      }

      // Call the upload callback with the new QR data
      if (onUpload) {
        onUpload(qrData);
      }

      setUploadError(null);
    } catch (err) {
      console.error("Error scanning QR code:", err);
      setUploadError(
        "Failed to read QR code from image. Please make sure the image contains a valid QR code.",
      );
    } finally {
      setUploading(false);
    }
  };

  if (loading) {
    return <div className={className}>Loading QR code...</div>;
  }

  if (error) {
    return <div className={className}>Failed to load QR code</div>;
  }

  const imageElement = imageSrc ? (
    <img src={imageSrc} alt={alt} className={className} />
  ) : (
    <div
      className={className}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        border: "2px dashed #ccc",
        backgroundColor: "#f9f9f9",
      }}
    >
      <span style={{ color: "#666", fontSize: "12px" }}>No QR Code</span>
    </div>
  );

  if (clickable && imageSrc) {
    return (
      <div>
        <a
          href={imageSrc}
          download={`treasure-hunt-${qrCodeData.slice(-8)}.png`}
          title="Click to download QR code"
          style={{ cursor: "pointer" }}
        >
          {imageElement}
        </a>
        {allowUpload && onUpload && (
          <div style={{ marginTop: "8px" }}>
            <button
              onClick={handleUploadClick}
              disabled={uploading}
              style={{
                padding: "6px 12px",
                fontSize: "12px",
                backgroundColor: "#8B4513",
                color: "#FFF8DC",
                border: "none",
                borderRadius: "4px",
                cursor: uploading ? "not-allowed" : "pointer",
                opacity: uploading ? 0.6 : 1,
                width: "80px",
              }}
            >
              {uploading ? "Uploading..." : "Upload"}
            </button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              style={{ display: "none" }}
            />
            {uploadError && (
              <div
                style={{
                  marginTop: "4px",
                  color: "#dc3545",
                  fontSize: "12px",
                }}
              >
                {uploadError}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div>
      {imageElement}
      {allowUpload && onUpload && (
        <div style={{ marginTop: "8px" }}>
          <button
            onClick={handleUploadClick}
            disabled={uploading}
            style={{
              padding: "6px 12px",
              fontSize: "12px",
              backgroundColor: "#8B4513",
              color: "#FFF8DC",
              border: "none",
              borderRadius: "4px",
              cursor: uploading ? "not-allowed" : "pointer",
              opacity: uploading ? 0.6 : 1,
              width: "80px",
            }}
          >
            {uploading ? "Uploading..." : "Upload"}
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            style={{ display: "none" }}
          />
          {uploadError && (
            <div
              style={{
                marginTop: "4px",
                color: "#dc3545",
                fontSize: "12px",
              }}
            >
              {uploadError}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
