{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^11.1.3", "@nestjs/typeorm": "^11.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.25", "rxjs": "^7.8.0", "multer": "^1.4.5-lts.1", "file-type": "^19.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.3", "@types/jest": "^29.5.0", "@types/node": "^24.0.7", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/multer": "^1.4.12", "jest": "^29.5.0", "nodemon": "^3.1.10", "sqlite3": "^5.1.6", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}