# Environment
NODE_ENV=development

# Database Configuration (should match docker-compose.yml)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=flashcards_user
DB_PASSWORD=flashcards_pass
DB_NAME=flashcards

# JWT Configuration
JWT_SECRET=CHANGE_ME

# Google OAuth Configuration
GOOGLE_CLIENT_ID=CHANGE_ME.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=CHANGE_ME
GOOGLE_CALLBACK_URL=CHANGE_ME/auth/google/callback