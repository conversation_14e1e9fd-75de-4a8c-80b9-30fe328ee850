import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Deck } from './deck.entity';
import { ReviewSession } from './review-session.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, name: 'google_id' })
  googleId: string;

  @Column()
  email: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  picture?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToMany(() => Deck, (deck) => deck.user)
  decks: Deck[];

  @OneToMany(() => ReviewSession, (reviewSession) => reviewSession.user)
  reviewSessions: ReviewSession[];
}
