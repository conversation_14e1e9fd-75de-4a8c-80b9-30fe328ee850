services:
  postgres:
    image: postgres:15
    container_name: flashcards-db
    environment:
      POSTGRES_DB: flashcards
      POSTGRES_USER: flashcards_user
      POSTGRES_PASSWORD: flashcards_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flashcards_user -d flashcards"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
