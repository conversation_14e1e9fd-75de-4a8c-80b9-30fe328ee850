{"name": "treasure_hunt_on_my_own", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "ts-node-dev ./src/server.ts", "test": "jest", "clean": "rm -rf node_modules yarn.lock && yarn cache clean"}, "dependencies": {"@prisma/client": "6.16.1", "express": "^5.1.0", "express-validator": "^7.2.1", "typescript": "^5.9.2", "uuid": "^13.0.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/express-validator": "^3.0.2", "@types/jest": "^30.0.0", "@types/node": "^24.3.3", "@types/supertest": "^6.0.3", "jest": "^30.2.0", "prisma": "^6.16.1", "supertest": "^7.1.4", "ts-jest": "^29.4.4", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0"}}