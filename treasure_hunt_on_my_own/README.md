```
docker compose up -d

yarn prisma migrate dev --name init
yarn prisma migrate

docker compose exec postgres psql -U treasure_hunt_on_my_own -d treasure_hunt

yarn test


--hunts
200
X curl -i http://localhost:3000/api/hunts
X curl -i http://localhost:3000/api/hunts/1
X curl -i -X PATCH http://localhost:3000/api/hunts/1 -H "Content-Type: application/json" -d '{"title":"hunt #1 meow"}'

201
X curl -i -X POST http://localhost:3000/api/hunts -H "Content-Type: application/json" -d '{"title":"hunt #1"}'

204
X curl -i -X DELETE http://localhost:3000/api/hunts/1

400
X curl -i http://localhost:3000/api/hunts/q
X curl -i -X POST http://localhost:3000/api/hunts
X curl -i -X PATCH http://localhost:3000/api/hunts/q
X curl -i -X PATCH http://localhost:3000/api/hunts/3 -H "Content-Type: application/json" -d '{}'
X curl -i -X PATCH http://localhost:3000/api/hunts/q -H "Content-Type: application/json" -d '{"title":"hunt #1"}'

404
X curl -i http://localhost:3000/api/foo
X curl -i http://localhost:3000/api/hunts/99
X curl -i -X PATCH http://localhost:3000/api/hunts/99 -H "Content-Type: application/json" -d '{"title":"hunt #1"}'
X curl -i -X DELETE http://localhost:3000/api/hunts/99


--treasures
200
curl -i http://localhost:3000/api/hunts/1/treasures
curl -i http://localhost:3000/api/hunts/1/treasures/1
curl -i -X PATCH http://localhost:3000/api/hunts/1/treasures/1 -H "Content-Type: application/json" -d '{}'
curl -i -X PATCH http://localhost:3000/api/hunts/1/treasures/1 -H "Content-Type: application/json" -d '{"clue": {"text":"foo4"}}'
curl -i -X PATCH http://localhost:3000/api/hunts/1/treasures/1 -H "Content-Type: application/json" -d '{"qrCodeData":"treasure-9a8fde05-b759-4a12-bc37-b4572c04eex1","clue": {"text":"foo2"}}'

201
curl -i -X POST http://localhost:3000/api/hunts/1/treasures -H "Content-Type: application/json" -d '{"clue": {"text":"foo"}}'

204
curl -i -X DELETE http://localhost:3000/api/hunts/1/treasures/1

400
curl -i http://localhost:3000/api/hunts/q/treasures
curl -i http://localhost:3000/api/hunts/q/treasures/1
curl -i http://localhost:3000/api/hunts/1/treasures/q
curl -i -X POST http://localhost:3000/api/hunts/1/treasures
curl -i -X POST http://localhost:3000/api/hunts/1/treasures -H "Content-Type: application/json" -d '{}'
curl -i -X POST http://localhost:3000/api/hunts/1/treasures -H "Content-Type: application/json" -d '{"clue": {}}'
curl -i -X POST http://localhost:3000/api/hunts/1/treasures -H "Content-Type: application/json" -d '{"qrCodeData":"invalid","clue": {"text":"foo"}}'
curl -i -X PATCH http://localhost:3000/api/hunts/q/treasures/1 -H "Content-Type: application/json" -d '{}'
curl -i -X PATCH http://localhost:3000/api/hunts/1/treasures/q -H "Content-Type: application/json" -d '{}'
curl -i -X DELETE http://localhost:3000/api/hunts/q/treasures/1
curl -i -X DELETE http://localhost:3000/api/hunts/1/treasures/q

404
curl -i http://localhost:3000/api/hunts/99/treasures
curl -i http://localhost:3000/api/hunts/1/treasures/99
curl -i -X POST http://localhost:3000/api/hunts/99/treasures -H "Content-Type: application/json" -d '{"clue": {"text":"foo"}}'
curl -i -X PATCH http://localhost:3000/api/hunts/99/treasures/1 -H "Content-Type: application/json" -d '{}'
curl -i -X PATCH http://localhost:3000/api/hunts/1/treasures/99 -H "Content-Type: application/json" -d '{}'
curl -i -X DELETE http://localhost:3000/api/hunts/99/treasures/1
curl -i -X DELETE http://localhost:3000/api/hunts/1/treasures/99

```
