services:
  postgres:
    image: postgres:15-alpine
    container_name: treasure_hunt_on_my_own_db
    environment:
      POSTGRES_DB: treasure_hunt
      POSTGRES_USER: treasure_hunt_on_my_own
      POSTGRES_PASSWORD: password_on_my_own
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test:
        ["CMD-SHELL", "pg_isready -U treasure_hunt_on_my_own -d treasure_hunt"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
