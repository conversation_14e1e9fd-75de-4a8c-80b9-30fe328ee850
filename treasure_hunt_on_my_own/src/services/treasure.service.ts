import { PrismaClient } from "../../generated/prisma";
import { v4 as uuidv4 } from "uuid";

const prisma = new PrismaClient();

const QR_CODE_PREFIX = "treasure-";

export async function getNextOrdinal(huntId: number): Promise<number> {
  const result = await prisma.treasure.aggregate({
    where: { huntId: huntId },
    _max: { ordinal: true },
  });
  const maxOrdinal = result._max.ordinal || 0;
  return maxOrdinal + 1;
}

export function isValidQrCode(qrCode: string): boolean {
  return qrCode.startsWith(QR_CODE_PREFIX);
}

export function generateQrCode(): string {
  return QR_CODE_PREFIX + uuidv4();
}
