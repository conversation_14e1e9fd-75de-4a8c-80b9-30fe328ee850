import { NextFunction, Request, Response } from "express";

export class HttpError extends Error {
  status: number;

  constructor(message: string, status?: number) {
    super(message);
    this.status = status ? status : 500;
  }
}

export class HttpNotFoundError extends HttpError {
  constructor(message?: string) {
    super(message ? message : "Not found", 404);
  }
}

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // console.error(err);
  const status: number = "status" in err ? Number(err.status) : 500;
  res.status(status).send({ errors: [{ message: err.message }] });
};

export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  res.status(404).send({ errors: [{ message: "Not found" }] });
};
