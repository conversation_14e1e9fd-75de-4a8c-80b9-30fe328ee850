jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid-1234"),
}));
jest.mock("../../generated/prisma");
jest.mock("../services/hunt.service");

import { PrismaClient } from "../../generated/prisma";
import request from "supertest";
import app from "../app";
import * as huntService from "../services/hunt.service";
import { HttpNotFoundError } from "../middlewares/errors";

describe("GET /api/foo", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it("should return 404 for unknown endpoints", async () => {
    const response = await request(app).get("/api/foo");
    expect(response.statusCode).toBe(404);
  });
});
