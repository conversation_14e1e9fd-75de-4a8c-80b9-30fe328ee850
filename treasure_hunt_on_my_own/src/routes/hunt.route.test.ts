jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid-1234"),
}));
jest.mock("../../generated/prisma");
jest.mock("../services/hunt.service");

const mockPrismaInstance = {
  hunt: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

import { PrismaClient } from "../../generated/prisma";

(PrismaClient as jest.MockedClass<typeof PrismaClient>).mockImplementation(
  () => mockPrismaInstance as any
);

import request from "supertest";
import app from "../app";
import * as huntService from "../services/hunt.service";
import { HttpNotFoundError } from "../middlewares/errors";

const TEST_HUNT = {
  id: 1,
  title: "test hunt 1",
  createdAt: new Date().toString(),
  updatedAt: new Date().toString(),
};

describe("GET /api/hunts", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it("should return 200 and hunts", async () => {
    const mockHunt2 = {
      id: 2,
      title: "test hunt 2",
    };

    mockPrismaInstance.hunt.findMany.mockResolvedValue([TEST_HUNT, mockHunt2]);

    const response = await request(app).get("/api/hunts");
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual([TEST_HUNT, mockHunt2]);
  });
});

describe("GET /api/hunts/:huntId", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it("should return 200 and hunt for valid huntId", async () => {
    (huntService.getHunt as jest.Mock).mockResolvedValue(TEST_HUNT);

    const response = await request(app).get(`/api/hunts/${TEST_HUNT.id}`);
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual(TEST_HUNT);
  });

  it("should return 400 for invalid huntId", async () => {
    const response = await request(app).get("/api/hunts/q");
    expect(response.statusCode).toBe(400);
  });

  it("should return 404 if hunt not found", async () => {
    (huntService.getHunt as jest.Mock).mockImplementation(() => {
      throw new HttpNotFoundError();
    });

    const response = await request(app).get("/api/hunts/99");
    expect(response.statusCode).toBe(404);
  });
});

describe("POST /api/hunts", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it("should return 201 and new hunt", async () => {
    mockPrismaInstance.hunt.create.mockResolvedValue(TEST_HUNT);

    const response = await request(app)
      .post("/api/hunts")
      .send({ title: TEST_HUNT.title });
    expect(response.statusCode).toBe(201);
    expect(response.body).toEqual({
      ...TEST_HUNT,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  it("should return 400 for missing title", async () => {
    const response = await request(app).post("/api/hunts").send({});
    expect(response.statusCode).toBe(400);
  });

  it("should return 400 for invalid body", async () => {
    const response = await request(app).post("/api/hunts");
    expect(response.statusCode).toBe(400);
  });
});

describe("PATCH /api/hunts/:huntId", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it("should return 200 and updated hunt", async () => {
    mockPrismaInstance.hunt.update.mockResolvedValue(TEST_HUNT);

    const response = await request(app)
      .patch(`/api/hunts/${TEST_HUNT.id}`)
      .send({ title: TEST_HUNT.title });
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      ...TEST_HUNT,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  it("should return 400 for invalid huntId", async () => {
    const response = await request(app)
      .patch("/api/hunts/q")
      .send({ title: "test hunt 1" });
    expect(response.statusCode).toBe(400);
  });

  it("should return 400 for invalid body", async () => {
    const response = await request(app).patch("/api/hunts/1");
    expect(response.statusCode).toBe(400);
  });

  it("should return 400 for invalid huntId and invalid body", async () => {
    const response = await request(app).patch("/api/hunts/q");
    expect(response.statusCode).toBe(400);
  });

  it("should return 404 if hunt not found", async () => {
    mockPrismaInstance.hunt.update.mockImplementation(() => {
      throw new HttpNotFoundError();
    });

    const response = await request(app)
      .patch("/api/hunts/99")
      .send({ title: "test hunt 1" });
    expect(response.statusCode).toBe(404);
  });
});

describe("DELETE /api/hunts/:huntId", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it("should return 204 and no content", async () => {
    mockPrismaInstance.hunt.delete.mockResolvedValue(TEST_HUNT);

    const response = await request(app).delete("/api/hunts/1");
    expect(response.statusCode).toBe(204);
    expect(response.body).toEqual({});
  });

  it("should return 400 for invalid huntId", async () => {
    const response = await request(app).delete("/api/hunts/q");
    expect(response.statusCode).toBe(400);
  });

  it("should return 404 if hunt not found", async () => {
    mockPrismaInstance.hunt.delete.mockImplementation(() => {
      throw new HttpNotFoundError();
    });

    const response = await request(app).delete("/api/hunts/99");
    expect(response.statusCode).toBe(404);
  });
});

describe("GET /api/hunts/foo/bar/cheese", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it("should return 404 for unknown hunts endpoints", async () => {
    const response = await request(app).get("/api/hunts/foo/bar/cheese");
    expect(response.statusCode).toBe(404);
  });
});
