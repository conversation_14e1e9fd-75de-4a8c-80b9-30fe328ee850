import { PrismaClient } from "../../generated/prisma";
import { Request, Response, Router } from "express";
import { HttpError, HttpNotFoundError } from "../middlewares/errors";
import { body, param, validationResult } from "express-validator";
import { getHunt } from "../services/hunt.service";
import {
  generateQrCode,
  getNextOrdinal,
  isValidQrCode,
} from "../services/treasure.service";

const router = Router({ mergeParams: true });
const prisma = new PrismaClient();

router.get(
  "/",
  [param("huntId").isNumeric().withMessage("huntId is invalid")],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    await getHunt(huntId);

    const treasures = await prisma.treasure.findMany({
      where: { huntId: huntId },
      include: { clue: true },
    });
    res.json(treasures);
  }
);

router.get(
  "/:treasureId",
  [
    param("huntId").isNumeric().withMessage("huntId is invalid"),
    param("treasureId").isNumeric().withMessage("treasureId is invalid"),
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    const treasureId = Number(req.params.treasureId);
    const treasure = await prisma.treasure.findUnique({
      where: { huntId: huntId, id: treasureId },
      include: { clue: true },
    });
    if (!treasure) {
      throw new HttpNotFoundError();
    }
    res.json(treasure);
  }
);

router.post(
  "/",
  [
    param("huntId").isNumeric().withMessage("huntId is invalid"),
    body("clue.text").notEmpty().withMessage("clue text is required"),
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    await getHunt(huntId);
    console.log("a-1");

    let qrCodeData: string = req.body.qrCodeData;
    console.log("a-2:", qrCodeData);
    if (!qrCodeData) {
      console.log("a-3");
      qrCodeData = generateQrCode();
      console.log("a-4:", qrCodeData);
    } else if (!isValidQrCode(qrCodeData)) {
      console.log("a-5:", qrCodeData);
      throw new HttpError("qrCodeData is invalid", 400);
    }

    console.log("a-6");
    let ordinal = await getNextOrdinal(huntId);
    console.log("a-7");
    const treasure = await prisma.treasure.create({
      data: {
        huntId: huntId,
        ordinal: ordinal,
        qrCodeData: qrCodeData,
      },
    });
    console.log("a-8");

    let clueText: string = req.body.clue.text;
    const clue = await prisma.clue.create({
      data: {
        treasureId: treasure.id,
        text: clueText,
      },
    });

    let result = await prisma.treasure.findUnique({
      where: { huntId: huntId, id: treasure.id },
      include: { clue: true },
    });

    res.status(201).json(result);
  }
);

router.patch(
  "/:treasureId",
  [
    param("huntId").isNumeric().withMessage("huntId is invalid"),
    param("treasureId").isNumeric().withMessage("treasureId is invalid"),
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    const treasureId = Number(req.params.treasureId);
    let treasure = await prisma.treasure.findUnique({
      where: { huntId: huntId, id: treasureId },
      include: { clue: true },
    });
    if (!treasure) {
      throw new HttpNotFoundError();
    }

    let qrCodeData: string = treasure.qrCodeData;
    if (req.body.qrCodeData) {
      qrCodeData = req.body.qrCodeData;
      if (qrCodeData && !isValidQrCode(qrCodeData)) {
        throw new HttpError("qrCodeData is invalid", 400);
      }
    }

    let text: string = req.body.clue?.text || treasure.clue?.text || "";

    const result = await prisma.treasure.update({
      where: { huntId: huntId, id: treasureId },
      data: { qrCodeData: qrCodeData, clue: { update: { text: text } } },
      include: { clue: true },
    });

    res.status(200).json(result);
  }
);

router.delete(
  "/:treasureId",
  [
    param("huntId").isNumeric().withMessage("huntId is invalid"),
    param("treasureId").isNumeric().withMessage("treasureId is invalid"),
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    const treasureId = Number(req.params.treasureId);
    try {
      await prisma.treasure.delete({
        where: { huntId: huntId, id: treasureId },
      });
      res.status(204).end();
    } catch (error) {
      throw new HttpNotFoundError();
    }
  }
);
export default router;
