{"name": "backend", "version": "1.0.0", "description": "Rose Bud Thorn API", "main": "dist/main.js", "scripts": {"build": "npx nest build", "start": "npx nest start", "start:dev": "npx nest start --watch", "start:debug": "npx nest start --debug --watch", "start:prod": "node dist/src/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d typeorm.config.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d typeorm.config.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d typeorm.config.ts", "migration:show": "typeorm-ts-node-commonjs migration:show -d typeorm.config.ts", "clean": "rm -rf node_modules dist yarn.lock && yarn cache clean"}, "license": "MIT", "dependencies": {"@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@types/helmet": "^4.0.0", "cache-manager": "^7.0.1", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^17.2.0", "helmet": "^8.1.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "typeorm": "^0.3.25", "@nestjs/cli": "^11.0.7", "@types/express": "^4.17.21"}, "devDependencies": {"@nestjs/testing": "^11.1.5", "@types/jest": "^30.0.0", "@types/node": "^24.0.14", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "jest": "^30.0.4", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}