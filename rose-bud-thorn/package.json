{"name": "rose-bud-thorn", "version": "1.0.0", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"yarn backend:dev\" \"yarn frontend:dev\"", "backend:dev": "yarn workspace backend start:dev", "frontend:dev": "yarn workspace frontend dev", "build": "yarn workspace backend build && yarn workspace frontend build", "test": "yarn workspace backend test && yarn workspace frontend test", "test:all": "yarn workspace backend test && yarn workspace frontend test && yarn workspace backend test:e2e", "test:watch": "concurrently \"yarn workspace backend test:watch\" \"yarn workspace frontend test:watch\"", "db:up": "docker compose up -d", "db:down": "docker compose down", "migration:run": "yarn workspace backend migration:run", "migration:show": "yarn workspace backend migration:show", "prepare": "husky", "check-format": "prettier --check '**/*.ts' '**/*.tsx'", "format": "prettier --write '**/*.ts' '**/*.tsx'", "clean": "yarn workspace backend clean && yarn workspace frontend clean && rm -rf node_modules yarn.lock && yarn cache clean"}, "lint-staged": {"**/*.ts": "prettier --write --ignore-unknown", "**/*.tsx": "prettier --write --ignore-unknown"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "concurrently": "^8.2.2", "eslint-config-next": "^15.4.3", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14"}, "dependencies": {"eslint": "^9.31.0"}}