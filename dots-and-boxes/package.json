{"name": "new_project_template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky", "check-format": "prettier --check '**/*.ts' '**/*.tsx'", "format": "prettier --write '**/*.ts' '**/*.tsx'", "clean": "rm -rf node_modules .next yarn.lock && yarn cache clean"}, "dependencies": {"next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0"}, "lint-staged": {"**/*.ts": "prettier --write --ignore-unknown", "**/*.tsx": "prettier --write --ignore-unknown"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "husky": "^9.1.7", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "lint-staged": "^16.1.4", "prettier": "^3.6.2", "typescript": "^5"}}